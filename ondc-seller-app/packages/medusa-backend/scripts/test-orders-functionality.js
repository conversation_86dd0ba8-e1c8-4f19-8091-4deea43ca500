#!/usr/bin/env node

/**\n * Orders Endpoint Functionality Test\n * \n * This script helps you test the orders endpoint functionality\n * and provides solutions for authentication issues.\n */\n\nconst http = require('http');\nconst fs = require('fs');\nconst path = require('path');\n\n// Configuration\nconst config = {\n  host: 'localhost',\n  port: 9000,\n  tenant: 'tenant-electronics-001'\n};\n\n// Test the current endpoint\nasync function testCurrentEndpoint() {\n  console.log('🧪 Testing Current Orders Endpoint Functionality\\n');\n  \n  const result = await makeRequest('/admin/orders?limit=1', {\n    'x-tenant-id': config.tenant,\n    'Content-Type': 'application/json'\n  });\n  \n  console.log(`📊 Status: ${result.statusCode}`);\n  console.log(`📦 Response:`, result.data);\n  \n  return result;\n}\n\n// Test store orders endpoint (might have different auth)\nasync function testStoreEndpoint() {\n  console.log('\\n🧪 Testing Store Orders Endpoint (Alternative)\\n');\n  \n  const result = await makeRequest('/store/orders?limit=1', {\n    'x-tenant-id': config.tenant,\n    'Content-Type': 'application/json'\n  });\n  \n  console.log(`📊 Status: ${result.statusCode}`);\n  console.log(`📦 Response:`, result.data);\n  \n  return result;\n}\n\n// Make HTTP request\nfunction makeRequest(path, headers) {\n  return new Promise((resolve) => {\n    const options = {\n      hostname: config.host,\n      port: config.port,\n      path: path,\n      method: 'GET',\n      headers\n    };\n\n    const req = http.request(options, (res) => {\n      let data = '';\n      res.on('data', (chunk) => data += chunk);\n      res.on('end', () => {\n        try {\n          const response = JSON.parse(data);\n          resolve({ statusCode: res.statusCode, data: response });\n        } catch (error) {\n          resolve({ statusCode: res.statusCode, data });\n        }\n      });\n    });\n\n    req.on('error', (error) => {\n      resolve({ statusCode: 0, error: error.message });\n    });\n\n    req.end();\n  });\n}\n\n// Check if we can temporarily disable auth for testing\nfunction checkAuthBypass() {\n  console.log('\\n🔧 AUTHENTICATION BYPASS OPTIONS\\n');\n  \n  console.log('Option 1: Temporary Auth Bypass for Testing');\n  console.log('==========================================');\n  console.log('You can temporarily modify your orders route to bypass auth:');\n  console.log('');\n  console.log('1. Edit src/api/admin/orders/route.ts');\n  console.log('2. Add this at the top of the GET function:');\n  console.log('');\n  console.log('   // TEMPORARY: Skip auth for testing');\n  console.log('   if (req.query.test === \"true\") {');\n  console.log('     // Your existing code here...');\n  console.log('   }');\n  console.log('');\n  console.log('3. Test with: /admin/orders?test=true&limit=1');\n  console.log('4. Remove this code after testing');\n  console.log('');\n  \n  console.log('Option 2: Check Medusa Admin Authentication');\n  console.log('==========================================');\n  console.log('1. Start Medusa admin dashboard');\n  console.log('2. Login to admin panel');\n  console.log('3. Open browser developer tools');\n  console.log('4. Go to Network tab');\n  console.log('5. Make a request and copy the Authorization header');\n  console.log('6. Use that token in your tests');\n  console.log('');\n  \n  console.log('Option 3: Database Direct Test');\n  console.log('=============================');\n  console.log('Test your database connection directly:');\n  console.log('');\n  console.log('psql -h localhost -U strapi -d medusa_backend -c \"');\n  console.log('SELECT id, status, currency_code, customer_id, email ');\n  console.log('FROM \\\"order\\\" ');\n  console.log('WHERE tenant_id = \\'tenant-electronics-001\\' ');\n  console.log('AND deleted_at IS NULL ');\n  console.log('LIMIT 5;\"');\n  console.log('');\n}\n\n// Analyze the response\nfunction analyzeResponse(adminResult, storeResult) {\n  console.log('\\n📊 RESPONSE ANALYSIS\\n');\n  \n  // Admin endpoint analysis\n  console.log('🔍 Admin Endpoint (/admin/orders):');\n  if (adminResult.statusCode === 200) {\n    console.log('   ✅ Working perfectly!');\n    if (adminResult.data.orders) {\n      console.log(`   📦 Found ${adminResult.data.orders.length} orders`);\n      if (adminResult.data.orders.length > 0) {\n        const order = adminResult.data.orders[0];\n        console.log(`   📋 Sample order fields: ${Object.keys(order).join(', ')}`);\n      }\n    }\n  } else if (adminResult.statusCode === 401) {\n    console.log('   🔐 Requires authentication');\n  } else if (adminResult.statusCode === 403) {\n    console.log('   🚫 Forbidden - check permissions');\n  } else if (adminResult.statusCode === 404) {\n    console.log('   ❌ Endpoint not found');\n  } else {\n    console.log(`   ⚠️  Status: ${adminResult.statusCode}`);\n  }\n  \n  // Store endpoint analysis\n  console.log('\\n🔍 Store Endpoint (/store/orders):');\n  if (storeResult.statusCode === 200) {\n    console.log('   ✅ Working! (Alternative endpoint)');\n  } else if (storeResult.statusCode === 401) {\n    console.log('   🔐 Also requires authentication');\n  } else if (storeResult.statusCode === 404) {\n    console.log('   ❌ Store orders endpoint not available');\n  } else {\n    console.log(`   ⚠️  Status: ${storeResult.statusCode}`);\n  }\n}\n\n// Provide specific solutions\nfunction provideSolutions(adminResult, storeResult) {\n  console.log('\\n🎯 SPECIFIC SOLUTIONS FOR YOUR SITUATION\\n');\n  \n  if (adminResult.statusCode === 401 && storeResult.statusCode === 401) {\n    console.log('🔐 Both endpoints require authentication. Here\\'s what to do:');\n    console.log('');\n    console.log('IMMEDIATE SOLUTION (for testing):');\n    console.log('1. Temporarily disable auth in your orders route');\n    console.log('2. Test the core functionality');\n    console.log('3. Re-enable auth after confirming it works');\n    console.log('');\n    console.log('PRODUCTION SOLUTION:');\n    console.log('1. Set up proper Medusa admin authentication');\n    console.log('2. Get valid admin tokens');\n    console.log('3. Use tokens in your API calls');\n    \n  } else if (adminResult.statusCode === 200) {\n    console.log('🎉 Your admin endpoint is working perfectly!');\n    console.log('No authentication issues detected.');\n    \n  } else if (storeResult.statusCode === 200) {\n    console.log('🎉 Your store endpoint is working!');\n    console.log('Consider using store endpoint or fixing admin auth.');\n    \n  } else {\n    console.log('🔧 Multiple issues detected. Check:');\n    console.log('1. Server is running properly');\n    console.log('2. Database connectivity');\n    console.log('3. Route configuration');\n    console.log('4. Middleware setup');\n  }\n}\n\n// Create a test bypass file\nfunction createTestBypass() {\n  console.log('\\n📝 Creating test bypass file...');\n  \n  const bypassCode = `\n// TEMPORARY TEST BYPASS - Add this to your orders route.ts\n// Add this at the beginning of your GET function:\n\n// TEMPORARY: Bypass auth for testing\nif (req.query.bypass_auth === 'true') {\n  console.log('⚠️  [TEST] Bypassing authentication for testing');\n  // Continue with your existing code...\n  // (rest of your function remains the same)\n}\n\n// Then test with: /admin/orders?bypass_auth=true&limit=1\n// REMEMBER TO REMOVE THIS AFTER TESTING!\n`;\n  \n  try {\n    fs.writeFileSync('test-bypass-code.txt', bypassCode);\n    console.log('✅ Created test-bypass-code.txt with bypass code');\n  } catch (error) {\n    console.log('❌ Could not create bypass file:', error.message);\n  }\n}\n\n// Main test function\nasync function runFunctionalityTest() {\n  console.log('🚀 Orders Endpoint Functionality Analysis\\n');\n  console.log(`📡 Server: http://${config.host}:${config.port}`);\n  console.log(`🏢 Tenant: ${config.tenant}\\n`);\n  \n  // Test both endpoints\n  const adminResult = await testCurrentEndpoint();\n  const storeResult = await testStoreEndpoint();\n  \n  // Analyze results\n  analyzeResponse(adminResult, storeResult);\n  \n  // Provide solutions\n  provideSolutions(adminResult, storeResult);\n  \n  // Show bypass options\n  checkAuthBypass();\n  \n  // Create bypass file\n  createTestBypass();\n  \n  console.log('\\n📋 SUMMARY\\n');\n  \n  if (adminResult.statusCode === 200) {\n    console.log('🎉 SUCCESS: Your orders endpoint is working!');\n    console.log('✅ No authentication issues');\n    console.log('🚀 Ready for enhanced implementation');\n  } else if (adminResult.statusCode === 401) {\n    console.log('🔐 AUTHENTICATION REQUIRED');\n    console.log('✅ Endpoint exists and responds');\n    console.log('🔧 Need to set up authentication');\n    console.log('💡 Use bypass method for testing core functionality');\n  } else {\n    console.log('⚠️  ISSUES DETECTED');\n    console.log('🔧 Check server, database, and configuration');\n  }\n  \n  console.log('\\n🎯 NEXT STEPS:');\n  console.log('1. 🔧 Resolve authentication (use bypass for testing)');\n  console.log('2. 🧪 Test core functionality');\n  console.log('3. 📊 Run validation: node scripts/validate-orders-response.js');\n  console.log('4. 🚀 Consider enhanced implementation upgrade');\n}\n\n// Export for use in other scripts\nmodule.exports = { testCurrentEndpoint, testStoreEndpoint, makeRequest };\n\n// Run if called directly\nif (require.main === module) {\n  runFunctionalityTest();\n}"