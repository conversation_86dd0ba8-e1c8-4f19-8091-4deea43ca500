#!/usr/bin/env node

/**
 * Orders Response Validation Script
 * 
 * This script validates the orders endpoint response against:
 * 1. Medusa API documentation standards
 * 2. Expected field structure
 * 3. Data type validation
 * 4. Business logic consistency
 */

const http = require('http');

// Medusa Orders Schema Definition (based on documentation)
const MEDUSA_ORDER_SCHEMA = {
  // Basic order fields
  basic: {
    id: 'string',
    status: 'string',
    currency_code: 'string',
    email: 'string',
    display_id: 'number',
    created_at: 'string',
    updated_at: 'string',
    metadata: 'object'
  },
  
  // Customer fields
  customer: {
    customer_id: 'string',
    customer: {
      id: 'string',
      email: 'string',
      first_name: 'string',
      last_name: 'string',
      phone: 'string'
    }
  },
  
  // Address fields
  addresses: {
    shipping_address_id: 'string',
    billing_address_id: 'string',
    shipping_address: {
      id: 'string',
      first_name: 'string',
      last_name: 'string',
      address_1: 'string',
      city: 'string',
      postal_code: 'string',
      country_code: 'string'
    },
    billing_address: {
      id: 'string',
      first_name: 'string',
      last_name: 'string',
      address_1: 'string',
      city: 'string',
      postal_code: 'string',
      country_code: 'string'
    }
  },
  
  // Financial fields
  financial: {
    total: 'number',
    subtotal: 'number',
    tax_total: 'number',
    shipping_total: 'number',
    discount_total: 'number',
    paid_total: 'number',
    refunded_total: 'number'
  },
  
  // Items fields
  items: {
    items: [{
      id: 'string',
      title: 'string',
      quantity: 'number',
      unit_price: 'number',
      total: 'number',
      product: {
        id: 'string',
        title: 'string',
        handle: 'string'
      },
      variant: {
        id: 'string',
        title: 'string',
        sku: 'string'
      }
    }]
  },
  
  // Payment fields
  payments: {
    payments: [{
      id: 'string',
      amount: 'number',
      currency_code: 'string',
      payment_status: 'string'
    }]
  }
};

// Configuration
const config = {
  host: 'localhost',
  port: 9000,
  path: '/admin/orders',
  headers: {
    'x-tenant-id': 'tenant-electronics-001',
    'Authorization': 'Bearer your-token-here',
    'Content-Type': 'application/json'
  }
};

// Validation results
let validationResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

// Main validation function
async function validateOrdersResponse() {
  console.log('🔍 Orders Response Validation\n');
  console.log(`📡 Testing: http://${config.host}:${config.port}${config.path}`);
  console.log(`🏢 Tenant: ${config.headers['x-tenant-id']}\n`);
  
  try {
    const response = await fetchOrdersData();
    
    if (!response) {
      throw new Error('No response received');
    }
    
    // Run all validation checks
    validateResponseStructure(response);
    validateOrderFields(response);
    validateDataTypes(response);
    validateBusinessLogic(response);
    validateMedusaCompliance(response);
    
    // Show results
    showValidationResults();
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    showTroubleshootingGuide();
  }
}

// Fetch orders data
function fetchOrdersData() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: config.host,
      port: config.port,
      path: config.path + '?limit=3', // Get 3 orders for testing
      method: 'GET',
      headers: config.headers
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`📊 HTTP Status: ${res.statusCode}`);
          console.log(`📦 Response Size: ${data.length} bytes\n`);
          resolve(response);
        } catch (error) {
          reject(new Error(`JSON Parse Error: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Request Error: ${error.message}`));
    });

    req.end();
  });
}

// Validate basic response structure
function validateResponseStructure(response) {
  console.log('🔍 1. RESPONSE STRUCTURE VALIDATION\n');
  
  // Check required top-level fields
  const requiredFields = ['orders', 'count', 'offset', 'limit'];
  
  requiredFields.forEach(field => {
    if (response[field] !== undefined) {
      addResult('✅', `${field} field present`, 'passed');
    } else {
      addResult('❌', `${field} field missing`, 'failed');
    }
  });
  
  // Check orders array
  if (Array.isArray(response.orders)) {
    addResult('✅', `orders is array with ${response.orders.length} items`, 'passed');
  } else {
    addResult('❌', 'orders is not an array', 'failed');
  }
  
  // Check tenant info
  if (response._tenant) {
    addResult('✅', 'tenant filtering info present', 'passed');
  } else {
    addResult('⚠️', 'tenant filtering info missing', 'warning');
  }
  
  console.log('');
}

// Validate individual order fields
function validateOrderFields(response) {
  console.log('🔍 2. ORDER FIELDS VALIDATION\n');
  
  if (!response.orders || response.orders.length === 0) {
    addResult('⚠️', 'No orders to validate', 'warning');
    return;
  }
  
  const order = response.orders[0];
  
  // Basic fields validation
  console.log('📋 Basic Fields:');
  Object.keys(MEDUSA_ORDER_SCHEMA.basic).forEach(field => {
    if (order[field] !== undefined) {
      addResult('✅', `${field}: ${order[field]}`, 'passed');
    } else {
      addResult('❌', `${field}: missing`, 'failed');
    }
  });
  
  // Customer fields validation
  console.log('\n👤 Customer Fields:');
  if (order.customer_id) {
    addResult('✅', `customer_id: ${order.customer_id}`, 'passed');
  } else {
    addResult('❌', 'customer_id: missing', 'failed');
  }
  
  if (order.customer) {
    addResult('✅', 'customer object present', 'passed');
    Object.keys(MEDUSA_ORDER_SCHEMA.customer.customer).forEach(field => {
      if (order.customer[field] !== undefined) {
        addResult('✅', `customer.${field}: present`, 'passed');
      } else {
        addResult('⚠️', `customer.${field}: missing`, 'warning');
      }
    });
  } else {
    addResult('⚠️', 'customer object missing (only ID available)', 'warning');
  }
  
  // Address fields validation
  console.log('\n🏠 Address Fields:');
  ['shipping_address', 'billing_address'].forEach(addressType => {
    if (order[addressType]) {
      addResult('✅', `${addressType} object present`, 'passed');
    } else {
      addResult('⚠️', `${addressType} object missing`, 'warning');
    }
  });
  
  // Financial fields validation
  console.log('\n💰 Financial Fields:');
  Object.keys(MEDUSA_ORDER_SCHEMA.financial).forEach(field => {
    if (order[field] !== undefined) {
      addResult('✅', `${field}: ${order[field]}`, 'passed');
    } else {
      addResult('⚠️', `${field}: missing`, 'warning');
    }
  });
  
  // Items validation
  console.log('\n📦 Items Fields:');
  if (order.items && Array.isArray(order.items)) {
    addResult('✅', `items array present with ${order.items.length} items`, 'passed');
    
    if (order.items.length > 0) {
      const item = order.items[0];
      ['id', 'title', 'quantity', 'unit_price'].forEach(field => {
        if (item[field] !== undefined) {
          addResult('✅', `item.${field}: present`, 'passed');
        } else {
          addResult('⚠️', `item.${field}: missing`, 'warning');
        }
      });
    }
  } else {
    addResult('⚠️', 'items array missing', 'warning');
  }
  
  // Payment validation
  console.log('\n💳 Payment Fields:');
  if (order.payments && Array.isArray(order.payments)) {
    addResult('✅', `payments array present with ${order.payments.length} payments`, 'passed');
  } else {
    addResult('⚠️', 'payments array missing', 'warning');
  }
  
  console.log('');
}

// Validate data types
function validateDataTypes(response) {
  console.log('🔍 3. DATA TYPE VALIDATION\n');
  
  if (!response.orders || response.orders.length === 0) {
    addResult('⚠️', 'No orders to validate data types', 'warning');
    return;
  }
  
  const order = response.orders[0];
  
  // Type checks
  const typeChecks = [
    { field: 'id', expected: 'string', actual: typeof order.id },
    { field: 'status', expected: 'string', actual: typeof order.status },
    { field: 'display_id', expected: 'number', actual: typeof order.display_id },
    { field: 'created_at', expected: 'string', actual: typeof order.created_at },
    { field: 'metadata', expected: 'object', actual: typeof order.metadata }
  ];
  
  typeChecks.forEach(check => {
    if (order[check.field] !== undefined) {
      if (check.actual === check.expected) {
        addResult('✅', `${check.field} type: ${check.actual}`, 'passed');
      } else {
        addResult('❌', `${check.field} type: expected ${check.expected}, got ${check.actual}`, 'failed');
      }
    }
  });
  
  // Date validation
  if (order.created_at) {
    const date = new Date(order.created_at);
    if (!isNaN(date.getTime())) {
      addResult('✅', 'created_at is valid date', 'passed');
    } else {
      addResult('❌', 'created_at is invalid date format', 'failed');
    }
  }
  
  console.log('');
}

// Validate business logic
function validateBusinessLogic(response) {
  console.log('🔍 4. BUSINESS LOGIC VALIDATION\n');
  
  if (!response.orders || response.orders.length === 0) {
    addResult('⚠️', 'No orders to validate business logic', 'warning');
    return;
  }
  
  // Tenant consistency
  const requestedTenant = config.headers['x-tenant-id'];
  let tenantConsistent = true;
  
  response.orders.forEach((order, index) => {
    if (order.tenant_id !== requestedTenant) {
      addResult('❌', `Order ${index + 1} has wrong tenant_id: ${order.tenant_id}`, 'failed');
      tenantConsistent = false;
    }
  });
  
  if (tenantConsistent) {
    addResult('✅', 'All orders belong to correct tenant', 'passed');
  }
  
  // Count consistency
  if (response.count === response.orders.length) {
    addResult('✅', 'Count matches orders array length', 'passed');
  } else {
    addResult('⚠️', `Count (${response.count}) doesn't match orders length (${response.orders.length})`, 'warning');
  }
  
  // Financial calculations (if available)
  response.orders.forEach((order, index) => {
    if (order.items && order.total !== undefined) {
      const calculatedTotal = order.items.reduce((sum, item) => {
        return sum + ((item.unit_price || 0) * (item.quantity || 0));
      }, 0);
      
      const shippingTotal = order.shipping_total || 0;
      const expectedTotal = calculatedTotal + shippingTotal;
      
      if (Math.abs(order.total - expectedTotal) < 0.01) {
        addResult('✅', `Order ${index + 1} financial calculations correct`, 'passed');
      } else {
        addResult('⚠️', `Order ${index + 1} financial calculations may be incorrect`, 'warning');
      }
    }
  });
  
  console.log('');
}

// Validate Medusa API compliance
function validateMedusaCompliance(response) {
  console.log('🔍 5. MEDUSA API COMPLIANCE\n');
  
  // Check response format
  if (response.orders && typeof response.count === 'number') {
    addResult('✅', 'Response follows Medusa list format', 'passed');
  } else {
    addResult('❌', 'Response does not follow Medusa list format', 'failed');
  }
  
  // Check pagination fields
  const paginationFields = ['offset', 'limit'];
  paginationFields.forEach(field => {
    if (typeof response[field] === 'number') {
      addResult('✅', `Pagination field ${field} present`, 'passed');
    } else {
      addResult('⚠️', `Pagination field ${field} missing or invalid`, 'warning');
    }
  });
  
  // Check if this is basic or enhanced implementation
  if (response.orders && response.orders.length > 0) {
    const order = response.orders[0];
    const enhancedFields = ['customer', 'shipping_address', 'billing_address', 'items', 'payments'];
    const hasEnhancedFields = enhancedFields.some(field => order[field] !== undefined);
    
    if (hasEnhancedFields) {
      addResult('✅', 'Enhanced implementation detected', 'passed');
    } else {
      addResult('⚠️', 'Basic implementation detected - missing comprehensive fields', 'warning');
    }
  }
  
  console.log('');
}

// Add validation result
function addResult(icon, message, type) {
  console.log(`   ${icon} ${message}`);
  validationResults[type]++;
  validationResults.details.push({ icon, message, type });
}

// Show validation results summary
function showValidationResults() {
  console.log('📊 VALIDATION SUMMARY\n');
  
  const total = validationResults.passed + validationResults.failed + validationResults.warnings;
  const passRate = Math.round((validationResults.passed / total) * 100);
  
  console.log(`✅ Passed: ${validationResults.passed}`);
  console.log(`❌ Failed: ${validationResults.failed}`);
  console.log(`⚠️  Warnings: ${validationResults.warnings}`);
  console.log(`📊 Pass Rate: ${passRate}%\n`);
  
  // Overall assessment
  if (validationResults.failed === 0 && validationResults.warnings === 0) {
    console.log('🎉 EXCELLENT: All validations passed!');
  } else if (validationResults.failed === 0) {
    console.log('✅ GOOD: No critical failures, some enhancements possible');
  } else if (validationResults.failed <= 2) {
    console.log('⚠️  NEEDS ATTENTION: Some critical issues found');
  } else {
    console.log('❌ CRITICAL: Multiple failures detected');
  }
  
  console.log('\n🎯 RECOMMENDATIONS:\n');
  
  if (validationResults.failed > 0) {
    console.log('🔧 CRITICAL FIXES NEEDED:');
    validationResults.details
      .filter(r => r.type === 'failed')
      .forEach(r => console.log(`   ${r.icon} ${r.message}`));
    console.log('');
  }
  
  if (validationResults.warnings > 0) {
    console.log('💡 ENHANCEMENTS SUGGESTED:');
    console.log('   • Implement enhanced orders endpoint for complete data');
    console.log('   • Add customer, address, and item details');
    console.log('   • Include financial calculations and payment info');
    console.log('   • Consider upgrading to route-enhanced.ts');
    console.log('');
  }
  
  console.log('📋 NEXT STEPS:');
  console.log('   1. Fix any critical failures');
  console.log('   2. Consider implementing enhanced version');
  console.log('   3. Test with different tenants and scenarios');
  console.log('   4. Monitor performance with larger datasets');
}

// Show troubleshooting guide
function showTroubleshootingGuide() {
  console.log('\n🔧 TROUBLESHOOTING GUIDE\n');
  console.log('Common issues and solutions:');
  console.log('');
  console.log('❌ Connection refused:');
  console.log('   • Check if server is running: npm run dev');
  console.log('   • Verify port 9000 is correct');
  console.log('   • Check firewall settings');
  console.log('');
  console.log('❌ 404 Not Found:');
  console.log('   • Verify orders endpoint exists');
  console.log('   • Check route configuration');
  console.log('   • Ensure API path is correct');
  console.log('');
  console.log('❌ 500 Internal Server Error:');
  console.log('   • Check server logs for details');
  console.log('   • Verify database connectivity');
  console.log('   • Check tenant data exists');
  console.log('');
  console.log('❌ Empty response:');
  console.log('   • Verify tenant has orders');
  console.log('   • Check tenant_id header');
  console.log('   • Review database filters');
}

// Export for use in other scripts
module.exports = { validateOrdersResponse };

// Run if called directly
if (require.main === module) {
  validateOrdersResponse();
}