#!/bin/bash

# Quick Orders Endpoint Test Script
echo "🚀 Quick Orders Endpoint Test"
echo "=============================="
echo ""

# Configuration
HOST="localhost"
PORT="9000"
ENDPOINT="/admin/orders"
TENANT_ID="tenant-electronics-001"

echo "📡 Testing: http://${HOST}:${PORT}${ENDPOINT}"
echo "🏢 Tenant: ${TENANT_ID}"
echo ""

# Test basic connectivity
echo "🔍 Testing connectivity..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "x-tenant-id: ${TENANT_ID}" \
  "http://${HOST}:${PORT}${ENDPOINT}")

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ HTTP Status: ${HTTP_CODE} (OK)"
else
    echo "❌ HTTP Status: ${HTTP_CODE}"
    if [ "$HTTP_CODE" = "000" ]; then
        echo "💡 Server might not be running. Try: npm run dev"
    fi
fi

echo ""
echo "🔍 Getting response..."
RESPONSE=$(curl -s \
  -H "x-tenant-id: ${TENANT_ID}" \
  "http://${HOST}:${PORT}${ENDPOINT}?limit=1")

echo "Response preview:"
echo "${RESPONSE:0:300}..."

echo ""
echo "📊 Summary:"
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Endpoint is working"
    echo "💡 Run 'node scripts/validate-orders-response.js' for detailed analysis"
else
    echo "❌ Endpoint has issues - check server and database"
fi
