#!/usr/bin/env node

/**
 * Test Script for Enhanced Orders API
 * 
 * This script tests the enhanced orders endpoint to ensure all fields
 * are properly returned according to the Medusa documentation.
 */

const http = require('http');

// Configuration
const config = {
  host: 'localhost',
  port: 9000,
  path: '/admin/orders',
  headers: {
    'x-tenant-id': 'tenant-electronics-001',
    'Authorization': 'Bearer your-admin-token-here', // Replace with actual token
    'Content-Type': 'application/json'
  }
};

// Expected fields based on Medusa documentation
const expectedFields = {
  basic: [
    'id', 'status', 'currency_code', 'email', 'display_id',
    'created_at', 'updated_at', 'tenant_id', 'metadata'
  ],
  customer: [
    'customer_id', 'customer.id', 'customer.email', 'customer.first_name',
    'customer.last_name', 'customer.phone'
  ],
  addresses: [
    'shipping_address_id', 'billing_address_id',
    'shipping_address.address_1', 'shipping_address.city',
    'billing_address.address_1', 'billing_address.city'
  ],
  items: [
    'items', 'items[0].id', 'items[0].title', 'items[0].quantity',
    'items[0].unit_price', 'items[0].product', 'items[0].variant'
  ],
  financial: [
    'total', 'subtotal', 'tax_total', 'shipping_total',
    'discount_total', 'paid_total', 'pending_difference'
  ],
  payment: [
    'payments', 'payments[0].amount', 'payments[0].currency_code',
    'payments[0].payment_status'
  ]
};

// Test function
async function testEnhancedOrders() {
  console.log('🧪 Testing Enhanced Orders API\n');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: config.host,
      port: config.port,
      path: config.path + '?limit=1', // Get just one order for testing
      method: 'GET',
      headers: config.headers
    };

    const req = http.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          analyzeResponse(response);
          resolve(response);
        } catch (error) {
          console.error('❌ Failed to parse response:', error);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Request failed:', error);
      reject(error);
    });

    req.end();
  });
}

// Response analysis function
function analyzeResponse(response) {
  console.log('📊 RESPONSE ANALYSIS\n');
  
  // Check basic response structure
  if (!response.orders || !Array.isArray(response.orders)) {
    console.log('❌ Invalid response structure - missing orders array');
    return;
  }

  if (response.orders.length === 0) {
    console.log('⚠️  No orders found in response');
    return;
  }

  const order = response.orders[0];
  console.log(`✅ Found ${response.orders.length} order(s) in response`);
  console.log(`📋 Analyzing order: ${order.id || 'Unknown ID'}\n`);

  // Test each field category
  testFieldCategory('Basic Order Fields', expectedFields.basic, order);
  testFieldCategory('Customer Fields', expectedFields.customer, order);
  testFieldCategory('Address Fields', expectedFields.addresses, order);
  testFieldCategory('Item Fields', expectedFields.items, order);
  testFieldCategory('Financial Fields', expectedFields.financial, order);
  testFieldCategory('Payment Fields', expectedFields.payment, order);

  // Additional checks
  checkDataQuality(order);
  checkTenantFiltering(response);
}

// Test specific field category
function testFieldCategory(categoryName, fields, order) {
  console.log(`🔍 ${categoryName}:`);
  
  let passed = 0;
  let total = fields.length;

  fields.forEach(field => {
    const value = getNestedValue(order, field);
    if (value !== undefined && value !== null) {
      console.log(`   ✅ ${field}: ${typeof value === 'object' ? 'Object' : value}`);
      passed++;
    } else {
      console.log(`   ❌ ${field}: Missing`);
    }
  });

  console.log(`   📊 Score: ${passed}/${total} (${Math.round((passed/total)*100)}%)\n`);
}

// Get nested object value by path
function getNestedValue(obj, path) {
  if (path.includes('[0]')) {
    // Handle array access
    const parts = path.split('[0]');
    const arrayPath = parts[0];
    const remainingPath = parts[1].replace('.', '');
    
    const array = getNestedValue(obj, arrayPath);
    if (Array.isArray(array) && array.length > 0) {
      return remainingPath ? getNestedValue(array[0], remainingPath) : array[0];
    }
    return undefined;
  }

  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// Check data quality
function checkDataQuality(order) {
  console.log('🔍 DATA QUALITY CHECKS:');
  
  // Check financial calculations
  if (order.items && order.items.length > 0) {
    const calculatedTotal = order.items.reduce((sum, item) => {
      return sum + ((item.unit_price || 0) * (item.quantity || 0));
    }, 0);
    
    const shippingTotal = order.shipping_total || 0;
    const expectedTotal = calculatedTotal + shippingTotal;
    
    if (Math.abs(order.total - expectedTotal) < 0.01) {
      console.log('   ✅ Financial calculations are consistent');
    } else {
      console.log(`   ⚠️  Financial mismatch: Expected ${expectedTotal}, Got ${order.total}`);
    }
  }

  // Check address consistency
  if (order.shipping_address && order.billing_address) {
    console.log('   ✅ Both shipping and billing addresses present');
  } else if (order.shipping_address || order.billing_address) {
    console.log('   ⚠️  Only one address type present');
  } else {
    console.log('   ❌ No addresses found');
  }

  // Check customer data
  if (order.customer && order.customer_id) {
    if (order.customer.id === order.customer_id) {
      console.log('   ✅ Customer ID consistency verified');
    } else {
      console.log('   ❌ Customer ID mismatch');
    }
  }

  console.log('');
}

// Check tenant filtering
function checkTenantFiltering(response) {
  console.log('🔍 TENANT FILTERING CHECKS:');
  
  if (response._tenant && response._tenant.filtered) {
    console.log(`   ✅ Tenant filtering active: ${response._tenant.id}`);
    console.log(`   ✅ Filter method: ${response._tenant.method}`);
  } else {
    console.log('   ❌ Tenant filtering not detected');
  }

  // Check all orders have correct tenant_id
  const tenantId = config.headers['x-tenant-id'];
  const invalidOrders = response.orders.filter(order => order.tenant_id !== tenantId);
  
  if (invalidOrders.length === 0) {
    console.log(`   ✅ All orders belong to tenant: ${tenantId}`);
  } else {
    console.log(`   ❌ Found ${invalidOrders.length} orders with wrong tenant_id`);
  }

  console.log('');
}

// Performance test
async function performanceTest() {
  console.log('⚡ PERFORMANCE TEST\n');
  
  const startTime = Date.now();
  
  try {
    await testEnhancedOrders();
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`📊 PERFORMANCE RESULTS:`);
    console.log(`   ⏱️  Response time: ${duration}ms`);
    
    if (duration < 1000) {
      console.log('   ✅ Excellent performance (< 1s)');
    } else if (duration < 3000) {
      console.log('   ⚠️  Acceptable performance (1-3s)');
    } else {
      console.log('   ❌ Slow performance (> 3s) - optimization needed');
    }
    
  } catch (error) {
    console.log('   ❌ Performance test failed:', error.message);
  }
}

// Main test execution
async function runTests() {
  console.log('🚀 Enhanced Orders API Test Suite\n');
  console.log(`📡 Testing endpoint: http://${config.host}:${config.port}${config.path}`);
  console.log(`🏢 Tenant: ${config.headers['x-tenant-id']}\n`);
  
  try {
    await performanceTest();
    
    console.log('\n🎯 TEST SUMMARY:');
    console.log('   • Response structure validation');
    console.log('   • Field presence verification');
    console.log('   • Data quality checks');
    console.log('   • Tenant filtering validation');
    console.log('   • Performance measurement');
    
    console.log('\n✅ Test completed successfully!');
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('   1. Verify all expected fields are present');
    console.log('   2. Check financial calculation accuracy');
    console.log('   3. Ensure tenant isolation is working');
    console.log('   4. Monitor response times in production');
    console.log('   5. Test with different tenant IDs');
    
  } catch (error) {
    console.log('\n❌ Test failed:', error.message);
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('   1. Ensure the server is running on localhost:9000');
    console.log('   2. Check that the enhanced orders endpoint is deployed');
    console.log('   3. Verify the tenant ID exists in the database');
    console.log('   4. Update the Authorization header with a valid token');
    console.log('   5. Check database connectivity and schema');
  }
}

// Run the tests
if (require.main === module) {
  runTests();
}

module.exports = { testEnhancedOrders, analyzeResponse };