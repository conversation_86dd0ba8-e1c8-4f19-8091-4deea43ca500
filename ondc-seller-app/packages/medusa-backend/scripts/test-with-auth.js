#!/usr/bin/env node

/**
 * Orders Endpoint Test with Authentication Handling
 * 
 * This script tests the orders endpoint and handles various authentication scenarios
 */

const http = require('http');

// Configuration
const config = {
  host: 'localhost',
  port: 9000,
  path: '/admin/orders',
  tenant: 'tenant-electronics-001' // Change this to your tenant ID
};

// Test without authentication first
async function testWithoutAuth() {
  console.log('🔍 Testing without authentication...\n');
  
  return new Promise((resolve) => {
    const options = {
      hostname: config.host,
      port: config.port,
      path: config.path + '?limit=1',
      method: 'GET',
      headers: {
        'x-tenant-id': config.tenant,
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`📊 HTTP Status: ${res.statusCode}`);
        console.log(`📋 Response Headers:`, res.headers);
        
        try {
          const response = JSON.parse(data);
          console.log(`📦 Response:`, JSON.stringify(response, null, 2));
        } catch (error) {
          console.log(`📦 Raw Response: ${data}`);
        }
        
        resolve({ statusCode: res.statusCode, data });
      });
    });

    req.on('error', (error) => {
      console.error('❌ Request failed:', error.message);
      resolve({ statusCode: 0, error: error.message });
    });

    req.end();
  });
}

// Test with different authentication approaches
async function testWithAuth() {
  console.log('\n🔍 Testing with authentication approaches...\n');
  
  const authTests = [
    {
      name: 'Bearer Token (if you have one)',
      headers: {
        'x-tenant-id': config.tenant,
        'Authorization': 'Bearer your-token-here', // Replace with actual token
        'Content-Type': 'application/json'
      }
    },
    {
      name: 'API Key (if configured)',
      headers: {
        'x-tenant-id': config.tenant,
        'x-api-key': 'your-api-key-here', // Replace with actual API key
        'Content-Type': 'application/json'
      }
    },
    {
      name: 'Publishable Key (for store endpoints)',
      headers: {
        'x-tenant-id': config.tenant,
        'x-publishable-api-key': 'your-publishable-key', // Replace with actual key
        'Content-Type': 'application/json'
      }
    }
  ];
  
  for (const test of authTests) {
    console.log(`🔐 Testing: ${test.name}`);
    
    const result = await makeRequest(test.headers);
    
    if (result.statusCode === 200) {
      console.log('   ✅ Success! This authentication method works');
      return result;
    } else if (result.statusCode === 401) {
      console.log('   ❌ Still unauthorized');
    } else if (result.statusCode === 403) {
      console.log('   ❌ Forbidden - check permissions');
    } else {
      console.log(`   ⚠️  Status: ${result.statusCode}`);
    }
  }
  
  return null;
}

// Make HTTP request with given headers
function makeRequest(headers) {
  return new Promise((resolve) => {
    const options = {
      hostname: config.host,
      port: config.port,
      path: config.path + '?limit=1',
      method: 'GET',
      headers
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data });
        }
      });
    });

    req.on('error', (error) => {
      resolve({ statusCode: 0, error: error.message });
    });

    req.end();
  });
}

// Check if authentication is required
async function checkAuthRequirement() {
  console.log('🔍 Checking authentication requirements...\n');
  
  // Test a simple endpoint first
  const healthCheck = await makeRequest({
    'Content-Type': 'application/json'
  });
  
  if (healthCheck.statusCode === 0) {
    console.log('❌ Server is not running or not accessible');
    console.log('💡 Try: npm run dev');
    return false;
  }
  
  console.log(`📊 Server is running (status: ${healthCheck.statusCode})`);
  return true;
}

// Analyze the authentication setup
function analyzeAuthSetup() {
  console.log('\n📋 AUTHENTICATION ANALYSIS\n');
  
  console.log('🔍 Your endpoint returned 401 Unauthorized. This could mean:');
  console.log('');
  console.log('1. 🔐 Authentication is required for admin endpoints');
  console.log('   • You need a valid admin token');
  console.log('   • Check your Medusa admin authentication setup');
  console.log('');
  console.log('2. 🏢 Tenant-based authentication');
  console.log('   • Your tenant might require specific authentication');
  console.log('   • Check tenant configuration');
  console.log('');
  console.log('3. 🛡️ Middleware protection');
  console.log('   • Admin routes might be protected by middleware');
  console.log('   • Check src/api/middlewares.ts');
  console.log('');
  
  console.log('🔧 SOLUTIONS TO TRY:\n');
  
  console.log('Option 1: Disable authentication temporarily for testing');
  console.log('   • Comment out authentication middleware');
  console.log('   • Test the endpoint functionality');
  console.log('   • Re-enable authentication after testing');
  console.log('');
  
  console.log('Option 2: Get a valid admin token');
  console.log('   • Login to admin dashboard');
  console.log('   • Get token from browser developer tools');
  console.log('   • Use token in Authorization header');
  console.log('');
  
  console.log('Option 3: Check middleware configuration');
  console.log('   • Review src/api/middlewares.ts');
  console.log('   • Check if orders endpoint is protected');
  console.log('   • Verify tenant authentication setup');
  console.log('');
  
  console.log('Option 4: Test with store endpoint (if available)');
  console.log('   • Try /store/orders instead of /admin/orders');
  console.log('   • Store endpoints might have different auth requirements');
}

// Main test function
async function runAuthTests() {
  console.log('🚀 Orders Endpoint Authentication Test\n');
  console.log(`📡 Testing: http://${config.host}:${config.port}${config.path}`);
  console.log(`🏢 Tenant: ${config.tenant}\n`);
  
  // Check if server is running
  const serverRunning = await checkAuthRequirement();
  if (!serverRunning) {
    return;
  }
  
  // Test without auth
  const noAuthResult = await testWithoutAuth();
  
  if (noAuthResult.statusCode === 200) {
    console.log('\n🎉 SUCCESS: Endpoint works without authentication!');
    console.log('Your orders endpoint is functional.');
    return;
  }
  
  if (noAuthResult.statusCode === 401) {
    console.log('\n🔐 Authentication required - testing different methods...');
    
    const authResult = await testWithAuth();
    
    if (authResult && authResult.statusCode === 200) {
      console.log('\n🎉 SUCCESS: Found working authentication method!');
      console.log('Your orders endpoint is functional with authentication.');
    } else {
      console.log('\n❌ No working authentication method found.');
      analyzeAuthSetup();
    }
  } else {
    console.log(`\n⚠️  Unexpected status code: ${noAuthResult.statusCode}`);
    console.log('This might indicate a different issue.');
  }
  
  console.log('\n📋 NEXT STEPS:\n');
  console.log('1. 🔧 Fix authentication if needed');
  console.log('2. 🧪 Re-run test once authentication works');
  console.log('3. 📊 Run detailed validation: node scripts/validate-orders-response.js');
  console.log('4. 🚀 Consider upgrading to enhanced implementation');
}

// Export for use in other scripts
module.exports = { testWithoutAuth, testWithAuth, checkAuthRequirement };

// Run if called directly
if (require.main === module) {
  runAuthTests();
}