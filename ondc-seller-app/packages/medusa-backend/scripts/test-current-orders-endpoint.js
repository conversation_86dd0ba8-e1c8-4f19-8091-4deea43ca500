#!/usr/bin/env node

/**
 * Test Script for Current Orders API Endpoint
 * 
 * This script tests the current orders endpoint to verify:
 * 1. Basic functionality
 * 2. Response structure
 * 3. Tenant filtering
 * 4. Data accuracy
 * 5. Performance
 */

const http = require('http');
const https = require('https');

// Configuration
const config = {
  host: 'localhost',
  port: 9000,
  path: '/admin/orders',
  headers: {
    'x-tenant-id': 'tenant-electronics-001', // Change this to your tenant ID
    'Authorization': 'Bearer your-admin-token-here', // Replace with actual token if needed
    'Content-Type': 'application/json'
  }
};

// Test the current orders endpoint
async function testCurrentOrdersEndpoint() {
  console.log('🧪 Testing Current Orders API Endpoint\n');
  console.log(`📡 Endpoint: http://${config.host}:${config.port}${config.path}`);
  console.log(`🏢 Tenant: ${config.headers['x-tenant-id']}\n`);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: config.host,
      port: config.port,
      path: config.path + '?limit=5', // Get 5 orders for testing
      method: 'GET',
      headers: config.headers
    };

    const startTime = Date.now();
    
    const req = http.request(options, (res) => {
      let data = '';
      
      console.log(`📊 Response Status: ${res.statusCode}`);
      console.log(`📋 Response Headers:`, res.headers);
      console.log('');

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const response = JSON.parse(data);
          analyzeCurrentResponse(response, responseTime, res.statusCode);
          resolve(response);
        } catch (error) {
          console.error('❌ Failed to parse JSON response:', error);
          console.log('📄 Raw response:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Request failed:', error.message);
      console.log('\n🔧 TROUBLESHOOTING STEPS:');
      console.log('   1. Check if the server is running: npm run dev');
      console.log('   2. Verify the port (9000) is correct');
      console.log('   3. Ensure the orders endpoint exists');
      console.log('   4. Check database connectivity');
      reject(error);
    });

    req.end();
  });
}

// Analyze the response from current implementation
function analyzeCurrentResponse(response, responseTime, statusCode) {
  console.log('📊 RESPONSE ANALYSIS\n');
  
  // 1. Basic Response Structure Check
  console.log('🔍 1. BASIC STRUCTURE CHECK:');
  
  if (statusCode === 200) {
    console.log('   ✅ HTTP Status: 200 OK');
  } else {
    console.log(`   ❌ HTTP Status: ${statusCode}`);
  }
  
  if (response.orders && Array.isArray(response.orders)) {
    console.log(`   ✅ Orders array present: ${response.orders.length} orders`);
  } else {
    console.log('   ❌ Orders array missing or invalid');
    return;
  }
  
  if (typeof response.count === 'number') {
    console.log(`   ✅ Count field present: ${response.count}`);
  } else {
    console.log('   ❌ Count field missing or invalid');
  }
  
  if (response._tenant) {
    console.log(`   ✅ Tenant info present: ${response._tenant.id}`);
  } else {
    console.log('   ❌ Tenant info missing');
  }
  
  console.log('');
  
  // 2. Current Implementation Fields Check
  console.log('🔍 2. CURRENT IMPLEMENTATION FIELDS:');
  
  if (response.orders.length > 0) {
    const order = response.orders[0];
    const expectedFields = [
      'id', 'status', 'currency_code', 'display_id',
      'created_at', 'updated_at', 'tenant_id', 'metadata',
      'customer_id', 'email'
    ];
    
    let presentFields = 0;
    expectedFields.forEach(field => {
      if (order[field] !== undefined) {
        console.log(`   ✅ ${field}: ${typeof order[field] === 'object' ? 'Object' : order[field]}`);
        presentFields++;
      } else {
        console.log(`   ❌ ${field}: Missing`);
      }
    });
    
    console.log(`   📊 Field Coverage: ${presentFields}/${expectedFields.length} (${Math.round((presentFields/expectedFields.length)*100)}%)`);
  } else {
    console.log('   ⚠️  No orders to analyze');
  }
  
  console.log('');
  
  // 3. Missing Fields Analysis
  console.log('🔍 3. MISSING FIELDS ANALYSIS:');
  console.log('   ❌ Customer details (only ID available)');
  console.log('   ❌ Billing address');
  console.log('   ❌ Shipping address');
  console.log('   ❌ Order items/line items');
  console.log('   ❌ Financial totals (subtotal, tax, shipping)');
  console.log('   ❌ Payment information');
  console.log('   ❌ Product/variant details');
  console.log('   ❌ Fulfillment status');
  console.log('');
  
  // 4. Tenant Filtering Check
  console.log('🔍 4. TENANT FILTERING CHECK:');
  
  const requestedTenant = config.headers['x-tenant-id'];
  let tenantFilteringWorking = true;
  
  response.orders.forEach((order, index) => {
    if (order.tenant_id !== requestedTenant) {
      console.log(`   ❌ Order ${index + 1} has wrong tenant_id: ${order.tenant_id}`);
      tenantFilteringWorking = false;
    }
  });
  
  if (tenantFilteringWorking && response.orders.length > 0) {
    console.log(`   ✅ All orders belong to tenant: ${requestedTenant}`);
  } else if (response.orders.length === 0) {
    console.log(`   ⚠️  No orders found for tenant: ${requestedTenant}`);
  }
  
  console.log('');
  
  // 5. Performance Check
  console.log('🔍 5. PERFORMANCE CHECK:');
  console.log(`   ⏱️  Response time: ${responseTime}ms`);
  
  if (responseTime < 500) {
    console.log('   ✅ Excellent performance (< 500ms)');
  } else if (responseTime < 1000) {
    console.log('   ✅ Good performance (< 1s)');
  } else if (responseTime < 3000) {
    console.log('   ⚠️  Acceptable performance (1-3s)');
  } else {
    console.log('   ❌ Slow performance (> 3s)');
  }
  
  console.log('');
  
  // 6. Data Quality Check
  console.log('🔍 6. DATA QUALITY CHECK:');
  
  if (response.orders.length > 0) {
    const order = response.orders[0];
    
    // Check required fields
    if (order.id) {
      console.log('   ✅ Order ID present');
    } else {
      console.log('   ❌ Order ID missing');
    }
    
    if (order.status) {
      console.log(`   ✅ Order status: ${order.status}`);
    } else {
      console.log('   ❌ Order status missing');
    }
    
    if (order.currency_code) {
      console.log(`   ✅ Currency code: ${order.currency_code}`);
    } else {
      console.log('   ❌ Currency code missing');
    }
    
    if (order.created_at) {
      const createdDate = new Date(order.created_at);
      if (!isNaN(createdDate.getTime())) {
        console.log(`   ✅ Valid created_at date: ${createdDate.toISOString()}`);
      } else {
        console.log('   ❌ Invalid created_at date format');
      }
    } else {
      console.log('   ❌ created_at missing');
    }
  }
  
  console.log('');
  
  // 7. Summary and Recommendations
  console.log('📋 SUMMARY AND RECOMMENDATIONS:\n');
  
  if (statusCode === 200 && response.orders) {
    console.log('✅ CURRENT ENDPOINT STATUS: Working');
    console.log('📊 FUNCTIONALITY: Basic order listing with tenant filtering');
    console.log('⚠️  LIMITATIONS: Only basic fields, missing comprehensive order data');
    console.log('');
    console.log('🚀 RECOMMENDATIONS:');
    console.log('   1. ✅ Current endpoint works for basic order listing');
    console.log('   2. ⚠️  Consider upgrading to enhanced implementation for:');
    console.log('      • Complete customer information');
    console.log('      • Billing and shipping addresses');
    console.log('      • Order items with product details');
    console.log('      • Financial calculations');
    console.log('      • Payment information');
    console.log('   3. 🔧 To upgrade: Replace with route-enhanced.ts');
  } else {
    console.log('❌ CURRENT ENDPOINT STATUS: Not working properly');
    console.log('🔧 TROUBLESHOOTING NEEDED');
  }
}

// Test with different parameters
async function testWithDifferentParams() {
  console.log('\n🧪 TESTING WITH DIFFERENT PARAMETERS\n');
  
  const testCases = [
    { name: 'Default parameters', params: '' },
    { name: 'Limited results', params: '?limit=2' },
    { name: 'With offset', params: '?limit=2&offset=1' },
    { name: 'Different order', params: '?order=created_at' }
  ];
  
  for (const testCase of testCases) {
    console.log(`🔍 Testing: ${testCase.name}`);
    
    try {
      const response = await makeRequest(config.path + testCase.params);
      console.log(`   ✅ Success: ${response.orders?.length || 0} orders returned`);
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
    }
  }
}

// Helper function to make HTTP requests
function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: config.host,
      port: config.port,
      path: path,
      method: 'GET',
      headers: config.headers
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.end();
  });
}

// Main test execution
async function runTests() {
  console.log('🚀 Orders Endpoint Validation Suite\n');
  console.log('This script will test your current orders endpoint to verify:');
  console.log('• Basic functionality and response structure');
  console.log('• Tenant filtering accuracy');
  console.log('• Data quality and completeness');
  console.log('• Performance characteristics');
  console.log('• Parameter handling\n');
  
  try {
    // Test main endpoint
    await testCurrentOrdersEndpoint();
    
    // Test with different parameters
    await testWithDifferentParams();
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('');
    console.log('If current endpoint is working:');
    console.log('   1. ✅ Your basic orders endpoint is functional');
    console.log('   2. 🔄 Consider upgrading to enhanced version for complete data');
    console.log('   3. 📊 Monitor performance with larger datasets');
    console.log('');
    console.log('If current endpoint has issues:');
    console.log('   1. 🔧 Check server logs for detailed error information');
    console.log('   2. 🗄️  Verify database connectivity and schema');
    console.log('   3. 🏢 Ensure tenant data exists in database');
    console.log('   4. 🔑 Check authentication if required');
    
  } catch (error) {
    console.log('\n❌ Test suite failed:', error.message);
    console.log('\n🔧 TROUBLESHOOTING CHECKLIST:');
    console.log('   □ Server running on localhost:9000');
    console.log('   □ Database accessible');
    console.log('   □ Orders table exists');
    console.log('   □ Tenant data populated');
    console.log('   □ Network connectivity');
  }
}

// Export for use in other scripts
module.exports = { testCurrentOrdersEndpoint, analyzeCurrentResponse };

// Run if called directly
if (require.main === module) {
  runTests();
}