#!/usr/bin/env node

/**
 * Simple Orders Endpoint Test
 * Tests the orders endpoint and provides clear guidance
 */

const http = require('http');

// Configuration
const config = {
  host: 'localhost',
  port: 9000,
  tenant: 'tenant-electronics-001'
};

// Make HTTP request
function makeRequest(path, headers) {
  return new Promise((resolve) => {
    const options = {
      hostname: config.host,
      port: config.port,
      path: path,
      method: 'GET',
      headers
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data });
        }
      });
    });

    req.on('error', (error) => {
      resolve({ statusCode: 0, error: error.message });
    });

    req.end();
  });
}

// Test the orders endpoint
async function testOrdersEndpoint() {
  console.log('🧪 Simple Orders Endpoint Test\\n');
  console.log(`📡 Testing: http://${config.host}:${config.port}/admin/orders`);
  console.log(`🏢 Tenant: ${config.tenant}\\n`);
  
  const result = await makeRequest('/admin/orders?limit=1', {
    'x-tenant-id': config.tenant,
    'Content-Type': 'application/json'
  });
  
  console.log(`📊 HTTP Status: ${result.statusCode}`);
  
  if (result.statusCode === 200) {
    console.log('✅ SUCCESS: Endpoint is working!');
    console.log('📦 Response:', JSON.stringify(result.data, null, 2));
    
    if (result.data.orders) {
      console.log(`\\n📋 Found ${result.data.orders.length} orders`);
      if (result.data.orders.length > 0) {
        const order = result.data.orders[0];
        console.log(`🔍 Order fields: ${Object.keys(order).join(', ')}`);
      }
    }
    
  } else if (result.statusCode === 401) {
    console.log('🔐 AUTHENTICATION REQUIRED');
    console.log('📦 Response:', result.data);
    console.log('\\n💡 SOLUTIONS:');
    console.log('1. Get admin token from Medusa admin dashboard');
    console.log('2. Temporarily bypass auth for testing (see below)');
    console.log('3. Check if endpoint should be public');
    
  } else if (result.statusCode === 404) {
    console.log('❌ ENDPOINT NOT FOUND');
    console.log('🔧 Check if src/api/admin/orders/route.ts exists');
    
  } else if (result.statusCode === 0) {
    console.log('❌ CONNECTION FAILED');
    console.log('🔧 Check if server is running: npm run dev');
    
  } else {
    console.log(`⚠️  UNEXPECTED STATUS: ${result.statusCode}`);
    console.log('📦 Response:', result.data);
  }
  
  return result;
}

// Show authentication bypass method
function showAuthBypass() {
  console.log('\\n🔧 TEMPORARY AUTH BYPASS FOR TESTING\\n');
  console.log('To test your endpoint functionality without auth:');
  console.log('');
  console.log('1. Edit src/api/admin/orders/route.ts');
  console.log('2. Add this at the very beginning of the GET function:');
  console.log('');
  console.log('   // TEMPORARY: Bypass auth for testing');
  console.log('   if (req.query.test_bypass === "true") {');
  console.log('     console.log("⚠️  [TEST] Bypassing auth for testing");');
  console.log('     // Continue with existing code...');
  console.log('   }');
  console.log('');
  console.log('3. Test with: /admin/orders?test_bypass=true&limit=1');
  console.log('4. IMPORTANT: Remove this code after testing!');
}

// Show database test
function showDatabaseTest() {
  console.log('\\n🗄️  DATABASE DIRECT TEST\\n');
  console.log('Test your database directly:');
  console.log('');
  console.log('psql -h localhost -U strapi -d medusa_backend -c "');
  console.log('SELECT id, status, currency_code, customer_id, email');
  console.log('FROM \\"order\\"');
  console.log('WHERE tenant_id = \\'tenant-electronics-001\\'');
  console.log('AND deleted_at IS NULL');
  console.log('LIMIT 5;"');
}

// Main test
async function runTest() {
  const result = await testOrdersEndpoint();
  
  if (result.statusCode === 401) {
    showAuthBypass();
    showDatabaseTest();
  }
  
  console.log('\\n📋 SUMMARY\\n');
  
  if (result.statusCode === 200) {
    console.log('🎉 Your orders endpoint is working correctly!');
    console.log('✅ No authentication issues');
    console.log('🚀 Ready for enhanced implementation');
    
  } else if (result.statusCode === 401) {
    console.log('🔐 Authentication is required');
    console.log('✅ Endpoint exists and responds');
    console.log('💡 Use bypass method above to test functionality');
    
  } else {
    console.log('⚠️  Issues detected - check server and configuration');
  }
  
  console.log('\\n🎯 NEXT STEPS:');
  console.log('1. Resolve authentication (or use bypass for testing)');
  console.log('2. Verify endpoint returns correct data structure');
  console.log('3. Consider upgrading to enhanced implementation');
  console.log('4. Test with real tenant data');
}

// Run the test
if (require.main === module) {
  runTest();
}

module.exports = { testOrdersEndpoint, makeRequest };