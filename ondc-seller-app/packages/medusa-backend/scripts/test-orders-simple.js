#!/usr/bin/env node

const http = require('http');

// Test the orders endpoint
async function testOrders() {
  console.log('🧪 Testing Orders Endpoint');
  console.log('==========================');
  
  const options = {
    hostname: 'localhost',
    port: 9000,
    path: '/admin/orders?limit=1',
    method: 'GET',
    headers: {
      'x-tenant-id': 'tenant-electronics-001',
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data}`);
        
        if (res.statusCode === 200) {
          console.log('✅ SUCCESS: Endpoint working!');
          try {
            const json = JSON.parse(data);
            if (json.orders) {
              console.log(`Found ${json.orders.length} orders`);
            }
          } catch (e) {
            console.log('Response is not JSON');
          }
        } else if (res.statusCode === 401) {
          console.log('🔐 AUTHENTICATION REQUIRED');
          console.log('');
          console.log('To test without auth, add this to your route.ts:');
          console.log('');
          console.log('// TEMPORARY TEST - Add at start of GET function');
          console.log('if (req.query.test === "true") {');
          console.log('  console.log("Testing without auth");');
          console.log('  // your existing code here');
          console.log('}');
          console.log('');
          console.log('Then test: /admin/orders?test=true&limit=1');
        } else {
          console.log(`❌ Error: Status ${res.statusCode}`);
        }
        
        resolve(res.statusCode);
      });
    });

    req.on('error', (error) => {
      console.log('❌ Connection failed:', error.message);
      console.log('💡 Make sure server is running: npm run dev');
      resolve(0);
    });

    req.end();
  });
}

// Run the test
testOrders().then((status) => {
  console.log('');
  console.log('📋 Summary:');
  if (status === 200) {
    console.log('🎉 Your endpoint is working perfectly!');
  } else if (status === 401) {
    console.log('🔐 Endpoint exists but needs authentication');
    console.log('💡 Use the bypass method above to test functionality');
  } else if (status === 0) {
    console.log('❌ Server connection failed');
  } else {
    console.log('⚠️  Check server logs for details');
  }
});