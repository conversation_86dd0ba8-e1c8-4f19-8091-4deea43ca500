import { medusaIntegrationTestRunner } from "@medusajs/test-utils"
import { 
  adminHeaders,
  createAdminUser,
} from "@medusajs/test-utils"

jest.setTimeout(50000)

medusaIntegrationTestRunner({
  testSuite: ({ dbConnection, getContainer, api }) => {
    describe("API Endpoints Verification", () => {
      let adminUser
      let adminToken
      let publishableApiKey = "pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0"
      
      // Test data
      const testTenants = [
        "default",
        "tenant-electronics-001", 
        "tenant-fashion-002",
        "tenant-books-003"
      ]
      
      const testCustomer = {
        email: "<EMAIL>",
        password: "testpassword123",
        first_name: "Test",
        last_name: "Customer",
        phone: "+919876543210"
      }

      beforeAll(async () => {
        // Create admin user and get token
        adminUser = await createAdminUser(dbConnection, adminHeaders, {
          id: "admin_user",
          email: "<EMAIL>",
          password: "supersecret"
        })
        
        // Login to get admin token
        const loginResponse = await api.post("/auth/user/emailpass", {
          email: "<EMAIL>",
          password: "supersecret"
        })
        
        adminToken = loginResponse.data.token
      })

      describe("Authentication API", () => {
        test("should authenticate admin user successfully", async () => {
          const response = await api.post("/auth/user/emailpass", {
            email: "<EMAIL>",
            password: "supersecret"
          })
          
          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("token")
          expect(typeof response.data.token).toBe("string")
        })

        test("should reject invalid admin credentials", async () => {
          try {
            await api.post("/auth/user/emailpass", {
              email: "<EMAIL>",
              password: "wrongpassword"
            })
          } catch (error) {
            expect(error.response.status).toBe(401)
          }
        })

        test("should get current admin user", async () => {
          const response = await api.get("/admin/users/me", {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })
          
          expect(response.status).toBe(200)
          expect(response.data.user).toHaveProperty("email", "<EMAIL>")
        })
      })

      describe("Multi-Tenant Configuration API", () => {
        test.each(testTenants)("should get tenant configuration for %s", async (tenantId) => {
          const headers = {
            Authorization: `Bearer ${adminToken}`,
            ...(tenantId !== "default" && { "x-tenant-id": tenantId })
          }
          
          const response = await api.get("/admin/tenant", { headers })
          
          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("success", true)
          expect(response.data.tenant).toHaveProperty("id")
          expect(response.data.tenant).toHaveProperty("name")
          expect(response.data.tenant).toHaveProperty("settings")
        })

        test("should handle invalid tenant ID", async () => {
          try {
            await api.get("/admin/tenant", {
              headers: {
                Authorization: `Bearer ${adminToken}`,
                "x-tenant-id": "invalid-tenant"
              }
            })
          } catch (error) {
            expect(error.response.status).toBe(400)
          }
        })
      })

      describe("Store Information API", () => {
        test("should get store information", async () => {
          const response = await api.get("/store", {
            headers: {
              "x-publishable-api-key": publishableApiKey
            }
          })
          
          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("store")
          expect(response.data.store).toHaveProperty("id")
          expect(response.data.store).toHaveProperty("name")
        })
      })

      describe("Products API", () => {
        let productId: string
        let variantId: string

        test("should list products (admin)", async () => {
          const response = await api.get("/admin/products", {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })
          
          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("products")
          expect(Array.isArray(response.data.products)).toBe(true)
        })

        test("should list products (store)", async () => {
          const response = await api.get("/store/products", {
            headers: {
              "x-publishable-api-key": publishableApiKey
            }
          })
          
          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("products")
          expect(Array.isArray(response.data.products)).toBe(true)
        })

        test("should create a product", async () => {
          const productData = {
            title: "Test Product",
            handle: "test-product",
            description: "A test product for API verification",
            status: "published",
            variants: [
              {
                title: "Default Variant",
                prices: [
                  {
                    amount: 2999,
                    currency_code: "INR"
                  }
                ],
                manage_inventory: true,
                inventory_quantity: 100
              }
            ]
          }

          const response = await api.post("/admin/products", productData, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              "Content-Type": "application/json"
            }
          })
          
          expect(response.status).toBe(200)
          expect(response.data.product).toHaveProperty("id")
          expect(response.data.product.title).toBe(productData.title)
          
          productId = response.data.product.id
          variantId = response.data.product.variants[0].id
        })

        test("should get product by ID", async () => {
          if (!productId) {
            throw new Error("Product ID not available from previous test")
          }

          const response = await api.get(`/admin/products/${productId}`, {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })
          
          expect(response.status).toBe(200)
          expect(response.data.product).toHaveProperty("id", productId)
        })

        test("should update product", async () => {
          if (!productId) {
            throw new Error("Product ID not available from previous test")
          }

          const updateData = {
            title: "Updated Test Product",
            description: "Updated description"
          }

          const response = await api.post(`/admin/products/${productId}`, updateData, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              "Content-Type": "application/json"
            }
          })
          
          expect(response.status).toBe(200)
          expect(response.data.product.title).toBe(updateData.title)
        })

        test("should test multi-tenant product isolation", async () => {
          // Create product for electronics tenant
          const electronicsProductData = {
            title: "Electronics Product",
            handle: "electronics-product",
            description: "Product for electronics tenant",
            status: "published"
          }

          const electronicsResponse = await api.post("/admin/products", electronicsProductData, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              "x-tenant-id": "tenant-electronics-001",
              "Content-Type": "application/json"
            }
          })
          
          expect(electronicsResponse.status).toBe(200)
          const electronicsProductId = electronicsResponse.data.product.id

          // Try to access electronics product from fashion tenant
          try {
            await api.get(`/admin/products/${electronicsProductId}`, {
              headers: {
                Authorization: `Bearer ${adminToken}`,
                "x-tenant-id": "tenant-fashion-002"
              }
            })
            // Should not reach here if tenant isolation is working
            expect(true).toBe(false)
          } catch (error) {
            expect(error.response.status).toBe(404)
          }
        })
      })

      describe("Cart API", () => {
        let cartId: string

        test("should create a cart", async () => {
          const cartData = {
            region_id: "reg_01234567890",
            sales_channel_id: "sc_01234567890"
          }

          const response = await api.post("/store/carts", cartData, {
            headers: {
              "x-publishable-api-key": publishableApiKey,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.cart).toHaveProperty("id")
          expect(response.data.cart.items).toEqual([])

          cartId = response.data.cart.id
        })

        test("should get cart by ID", async () => {
          if (!cartId) {
            throw new Error("Cart ID not available from previous test")
          }

          const response = await api.get(`/store/carts/${cartId}`, {
            headers: {
              "x-publishable-api-key": publishableApiKey
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.cart).toHaveProperty("id", cartId)
        })

        test("should add item to cart", async () => {
          if (!cartId || !variantId) {
            throw new Error("Cart ID or Variant ID not available from previous tests")
          }

          const itemData = {
            variant_id: variantId,
            quantity: 2,
            metadata: {
              test_item: true
            }
          }

          const response = await api.post(`/store/carts/${cartId}/line-items`, itemData, {
            headers: {
              "x-publishable-api-key": publishableApiKey,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.cart.items.length).toBeGreaterThan(0)
        })

        test("should update cart item quantity", async () => {
          if (!cartId) {
            throw new Error("Cart ID not available from previous test")
          }

          // First get the cart to find line item ID
          const cartResponse = await api.get(`/store/carts/${cartId}`, {
            headers: {
              "x-publishable-api-key": publishableApiKey
            }
          })

          const lineItemId = cartResponse.data.cart.items[0].id

          const updateData = {
            quantity: 3
          }

          const response = await api.post(`/store/carts/${cartId}/line-items/${lineItemId}`, updateData, {
            headers: {
              "x-publishable-api-key": publishableApiKey,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.cart.items[0].quantity).toBe(3)
        })

        test("should add shipping address to cart", async () => {
          if (!cartId) {
            throw new Error("Cart ID not available from previous test")
          }

          const addressData = {
            first_name: "John",
            last_name: "Doe",
            address_1: "123 Test Street",
            city: "Mumbai",
            postal_code: "400001",
            country_code: "IN",
            phone: "+919876543210"
          }

          const response = await api.post(`/store/carts/${cartId}/shipping-address`, addressData, {
            headers: {
              "x-publishable-api-key": publishableApiKey,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.cart.shipping_address).toMatchObject(addressData)
        })
      })

      describe("Customer API", () => {
        let customerId: string
        let customerToken: string

        test("should create a customer", async () => {
          const response = await api.post("/store/customers", testCustomer, {
            headers: {
              "x-publishable-api-key": publishableApiKey,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.customer).toHaveProperty("id")
          expect(response.data.customer.email).toBe(testCustomer.email)

          customerId = response.data.customer.id
        })

        test("should authenticate customer", async () => {
          const loginData = {
            email: testCustomer.email,
            password: testCustomer.password
          }

          const response = await api.post("/auth/customer/emailpass", loginData, {
            headers: {
              "x-publishable-api-key": publishableApiKey,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("token")

          customerToken = response.data.token
        })

        test("should get customer profile", async () => {
          const response = await api.get("/store/customers/me", {
            headers: {
              "x-publishable-api-key": publishableApiKey,
              Authorization: `Bearer ${customerToken}`
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.customer).toHaveProperty("email", testCustomer.email)
        })

        test("should list customers (admin)", async () => {
          const response = await api.get("/admin/customers", {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })

          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("customers")
          expect(Array.isArray(response.data.customers)).toBe(true)
        })

        test("should get customer by ID (admin)", async () => {
          if (!customerId) {
            throw new Error("Customer ID not available from previous test")
          }

          const response = await api.get(`/admin/customers/${customerId}`, {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.customer).toHaveProperty("id", customerId)
        })

        test("should update customer (admin)", async () => {
          if (!customerId) {
            throw new Error("Customer ID not available from previous test")
          }

          const updateData = {
            first_name: "Updated",
            last_name: "Customer"
          }

          const response = await api.post(`/admin/customers/${customerId}`, updateData, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.customer.first_name).toBe(updateData.first_name)
        })
      })

      describe("Orders API", () => {
        let orderId: string

        test("should list orders (admin)", async () => {
          const response = await api.get("/admin/orders", {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })

          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("orders")
          expect(Array.isArray(response.data.orders)).toBe(true)
        })

        test("should create draft order (admin)", async () => {
          if (!customerId || !variantId) {
            throw new Error("Customer ID or Variant ID not available from previous tests")
          }

          const orderData = {
            customer_id: customerId,
            region_id: "reg_01234567890",
            items: [
              {
                variant_id: variantId,
                quantity: 1
              }
            ],
            shipping_address: {
              first_name: "John",
              last_name: "Doe",
              address_1: "123 Test Street",
              city: "Mumbai",
              postal_code: "400001",
              country_code: "IN"
            }
          }

          const response = await api.post("/admin/orders", orderData, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.order).toHaveProperty("id")
          expect(response.data.order.customer_id).toBe(customerId)

          orderId = response.data.order.id
        })

        test("should get order by ID (admin)", async () => {
          if (!orderId) {
            throw new Error("Order ID not available from previous test")
          }

          const response = await api.get(`/admin/orders/${orderId}`, {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.order).toHaveProperty("id", orderId)
        })

        test("should update order status (admin)", async () => {
          if (!orderId) {
            throw new Error("Order ID not available from previous test")
          }

          const updateData = {
            status: "fulfilled",
            metadata: {
              tracking_number: "TRK123456789"
            }
          }

          const response = await api.post(`/admin/orders/${orderId}`, updateData, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.order.status).toBe(updateData.status)
        })
      })

      describe("Categories API", () => {
        let categoryId: string

        test("should list product categories (store)", async () => {
          const response = await api.get("/store/product-categories", {
            headers: {
              "x-publishable-api-key": publishableApiKey
            }
          })

          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("product_categories")
          expect(Array.isArray(response.data.product_categories)).toBe(true)
        })

        test("should create product category (admin)", async () => {
          const categoryData = {
            name: "Test Category",
            handle: "test-category",
            description: "A test category for API verification",
            is_active: true
          }

          const response = await api.post("/admin/product-categories", categoryData, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.product_category).toHaveProperty("id")
          expect(response.data.product_category.name).toBe(categoryData.name)

          categoryId = response.data.product_category.id
        })

        test("should get category by ID (admin)", async () => {
          if (!categoryId) {
            throw new Error("Category ID not available from previous test")
          }

          const response = await api.get(`/admin/product-categories/${categoryId}`, {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.product_category).toHaveProperty("id", categoryId)
        })

        test("should update category (admin)", async () => {
          if (!categoryId) {
            throw new Error("Category ID not available from previous test")
          }

          const updateData = {
            name: "Updated Test Category",
            description: "Updated description"
          }

          const response = await api.post(`/admin/product-categories/${categoryId}`, updateData, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              "Content-Type": "application/json"
            }
          })

          expect(response.status).toBe(200)
          expect(response.data.product_category.name).toBe(updateData.name)
        })
      })

      describe("Regions API", () => {
        test("should list regions (admin)", async () => {
          const response = await api.get("/admin/regions", {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })

          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("regions")
          expect(Array.isArray(response.data.regions)).toBe(true)
        })
      })

      describe("Sales Channels API", () => {
        test("should list sales channels (admin)", async () => {
          const response = await api.get("/admin/sales-channels", {
            headers: {
              Authorization: `Bearer ${adminToken}`
            }
          })

          expect(response.status).toBe(200)
          expect(response.data).toHaveProperty("sales_channels")
          expect(Array.isArray(response.data.sales_channels)).toBe(true)
        })
      })

      describe("Error Handling", () => {
        test("should return 401 for unauthorized admin requests", async () => {
          try {
            await api.get("/admin/products")
          } catch (error) {
            expect(error.response.status).toBe(401)
          }
        })

        test("should return 404 for non-existent resources", async () => {
          try {
            await api.get("/admin/products/non-existent-id", {
              headers: {
                Authorization: `Bearer ${adminToken}`
              }
            })
          } catch (error) {
            expect(error.response.status).toBe(404)
          }
        })

        test("should return 400 for invalid request data", async () => {
          try {
            await api.post("/admin/products", { invalid: "data" }, {
              headers: {
                Authorization: `Bearer ${adminToken}`,
                "Content-Type": "application/json"
              }
            })
          } catch (error) {
            expect(error.response.status).toBe(400)
          }
        })
      })
    })
  }
})
