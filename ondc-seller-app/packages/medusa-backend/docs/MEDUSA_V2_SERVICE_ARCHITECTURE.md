# Medusa v2 Service Architecture Documentation

**Generated:** 2025-08-07T07:53:41.640Z  
**Total Services Discovered:** 35

## 🎯 **Service Resolution Results**

### ✅ **Successfully Resolved Core Services**

| Service Type | Resolved Name | Constructor | CRUD Methods | Query Methods |
|-------------|---------------|-------------|--------------|---------------|
| **product** | `product` | ProductModuleService | 23 | 4 |
| **customer** | `customer` | CustomerModuleService | 9 | 0 |
| **order** | `order` | OrderModuleService | 37 | 14 |
| **cart** | `cart` | CartModuleService | 12 | 3 |
| **inventory** | `inventory` | InventoryModuleService | 16 | 4 |
| **pricing** | `pricing` | PricingModuleService | 22 | 3 |

### 🔧 **Recommended Service Names for Multi-Tenant Integration**

```typescript
// ✅ CONFIRMED WORKING SERVICE NAMES
const MEDUSA_V2_SERVICES = {
  product: 'product',           // ProductModuleService
  customer: 'customer',         // CustomerModuleService  
  order: 'order',              // OrderModuleService
  cart: 'cart',                // CartModuleService
  inventory: 'inventory',       // InventoryModuleService
  pricing: 'pricing'           // PricingModuleService
};

// Usage in routes:
const productService = req.scope.resolve('product');
const customerService = req.scope.resolve('customer');
const orderService = req.scope.resolve('order');
```

## 📊 **Service Categories**

### **Core Services (13)**
- `auth` - Authentication service (9 methods)
- `cart` - Cart management (35 methods)
- `currency` - Currency handling (3 methods)
- `customer` - Customer management (11 methods)
- `fulfillment` - Order fulfillment (54 methods)
- `inventory` - Inventory management (32 methods)
- `order` - Order processing (94 methods)
- `payment` - Payment processing (26 methods)
- `product` - Product management (39 methods)
- `promotion` - Promotions & discounts (26 methods)
- `region` - Regional settings (6 methods)
- `sales_channel` - Sales channels (5 methods)
- `tax` - Tax calculations (19 methods)
- `user` - User management (9 methods)

### **Modules (2)**
- `configModule` - Configuration management
- `storeAnalyticsModuleService` - Analytics module

### **Custom Services (20)**
- `api_key` - API key management
- `cache` - Caching service
- `event_bus` - Event handling
- `file` - File management
- `link` - Entity linking
- `locking` - Resource locking
- `logger` - Logging service
- `notification` - Notifications
- `pricing` - Pricing calculations
- `query` - Query service
- `remoteLink` - Remote linking
- `remoteQuery` - Remote queries
- `stock_location` - Stock locations
- `store` - Store management
- `workflows` - Workflow engine
- And 5 more...

## 🏗️ **Service Method Analysis**

### **Product Service Methods**
```typescript
// CRUD Operations (23 methods)
createProducts, createProducts_, updateProducts, updateProducts_
createProductVariants, updateProductVariants
createProductTags, updateProductTags
createProductTypes, updateProductTypes
createProductOptions, updateProductOptions
createProductCollections, updateProductCollections
createProductCategories, updateProductCategories

// Query Operations (4 methods)
retrieveProduct, listProducts, listAndCountProducts
getProductFindConfig_

// Utility Methods
normalizeCreateProductInput, normalizeUpdateProductInput
validateProductPayload, validateProductCreatePayload
upsertProductVariants, upsertProductTags, etc.
```

### **Customer Service Methods**
```typescript
// CRUD Operations (9 methods)
createCustomers, createCustomers_, updateCustomers
createCustomerGroups, updateCustomerGroups
createCustomerAddresses, createCustomerAddresses_, updateCustomerAddresses

// Utility Methods
addCustomerToGroup, removeCustomerFromGroup, flush
```

### **Order Service Methods**
```typescript
// CRUD Operations (37 methods)
createOrders, createOrders_, updateOrders, updateOrders_
createOrderLineItems, updateOrderLineItems
createClaim, createExchange, createReturn
cancelClaim, cancelExchange, cancelReturn

// Query Operations (14 methods)
listOrders, listAndCountOrders, retrieveOrder
listOrderClaims, listOrderExchanges, listReturns
retrieveOrderClaim, retrieveOrderExchange, retrieveReturn

// Business Logic (43+ methods)
completeOrder, cancelOrder, registerDelivery
registerFulfillment, registerShipment
previewOrderChange, confirmOrderChange, etc.
```

## 🎯 **Multi-Tenant Integration Strategy**

### **Recommended Approach: Service Override Pattern**

Based on the service discovery, the recommended approach is to **override core Medusa services** with tenant-aware versions:

```typescript
// 1. Create tenant-aware service overrides
class TenantAwareProductService extends ProductModuleService {
  async createProducts(data, context) {
    // Automatically inject tenant_id
    const tenantId = context.tenant_id || 'default';
    const enrichedData = data.map(item => ({ ...item, tenant_id: tenantId }));
    return super.createProducts(enrichedData, context);
  }
  
  async listProducts(filters, config, context) {
    // Automatically filter by tenant
    const tenantId = context.tenant_id || 'default';
    const tenantFilters = { ...filters, tenant_id: tenantId };
    return super.listProducts(tenantFilters, config, context);
  }
}

// 2. Register overrides in container
container.register({
  product: asClass(TenantAwareProductService).singleton()
});
```

### **Service Resolution Patterns**
```typescript
// ✅ WORKING - Direct service name resolution
const productService = req.scope.resolve('product');
const customerService = req.scope.resolve('customer');
const orderService = req.scope.resolve('order');

// ❌ NOT WORKING - Legacy patterns
const productService = req.scope.resolve('productService');
const productService = req.scope.resolve('productModuleService');
```

## 🚀 **Next Steps for Implementation**

1. **✅ Service Names Identified** - Use the confirmed service names above
2. **🔄 Create Service Overrides** - Implement tenant-aware service wrappers
3. **🔄 Test CRUD Operations** - Validate tenant context injection
4. **🔄 Query Schema Extension** - Handle tenant_id in query parameters

## 📋 **Container Information**

- **Total Services:** 35
- **Container Type:** Awilix-based dependency injection
- **Available Methods:** inspect, loadModules, createScope, register, build, resolve, hasRegistration, dispose, getRegistration, registerAdd
- **Cradle Keys:** 35 registered services

## 🎉 **Task 3.1 Completion**

**✅ COMPLETED:** Medusa v2 service names successfully identified and documented!

**Key Findings:**
- Core services use simple names: `product`, `customer`, `order`, `cart`
- All services are properly registered in the container
- Services have rich method sets for CRUD operations
- Service override pattern is the recommended approach for multi-tenancy
