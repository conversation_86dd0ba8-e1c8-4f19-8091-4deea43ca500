# Tenant Context Usage Guide

## Overview

This guide documents how to access and use tenant context in Medusa v2 multi-tenant applications. The tenant context system provides standardized access to tenant information across all request handlers.

## Table of Contents

- [Quick Start](#quick-start)
- [Core Utilities](#core-utilities)
- [Data Operations](#data-operations)
- [Response Utilities](#response-utilities)
- [Feature Management](#feature-management)
- [Best Practices](#best-practices)
- [Examples](#examples)

## Quick Start

### Basic Tenant Information Access

```typescript
import { getTenantId, getTenantConfig, getTenantContext } from '../utils/tenant-context';

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  // Get tenant ID (always available)
  const tenantId = getTenantId(req);
  
  // Get full tenant configuration
  const tenantConfig = getTenantConfig(req);
  
  // Get complete tenant context with metadata
  const tenantContext = getTenantContext(req);
  
  console.log(`Processing request for tenant: ${tenantId}`);
};
```

### Validating Tenant Context

```typescript
import { hasTenantContext, validateTenantContext } from '../utils/tenant-context';

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Quick check if tenant context is available
  if (!hasTenantContext(req)) {
    return res.status(400).json({ error: 'Tenant context missing' });
  }
  
  // Comprehensive validation
  const validation = validateTenantContext(req);
  if (!validation.isValid) {
    return res.status(400).json({
      error: 'Invalid tenant context',
      details: validation.errors
    });
  }
  
  // Proceed with tenant-aware logic
};
```

## Core Utilities

### getTenantId(req)
Returns the tenant ID from the request. Always returns a string (defaults to 'default').

```typescript
const tenantId = getTenantId(req); // 'tenant-electronics-001'
```

### getTenantConfig(req)
Returns the complete tenant configuration object or null if not available.

```typescript
const config = getTenantConfig(req);
if (config) {
  console.log(`Tenant: ${config.name}`);
  console.log(`Currency: ${config.settings.currency}`);
  console.log(`ONDC BPP ID: ${config.settings.ondcConfig.bppId}`);
}
```

### getTenantContext(req)
Returns comprehensive tenant context with metadata.

```typescript
const context = getTenantContext(req);
if (context) {
  console.log(`Tenant ID: ${context.id}`);
  console.log(`Is Active: ${context.isActive}`);
  console.log(`Features: ${context.features.join(', ')}`);
  console.log(`Extracted from: ${context.metadata.extractedFrom}`);
}
```

## Data Operations

### Injecting Tenant ID

```typescript
import { injectTenantId } from '../utils/tenant-context';

// Single object
const productData = { name: 'iPhone', price: 999 };
const tenantProduct = injectTenantId(req, productData);
// Result: { name: 'iPhone', price: 999, tenant_id: 'tenant-electronics-001' }

// Array of objects
const products = [
  { name: 'iPhone', price: 999 },
  { name: 'Samsung', price: 899 }
];
const tenantProducts = injectTenantId(req, products);
// Each item gets tenant_id added
```

### Filtering by Tenant

```typescript
import { filterByTenantId } from '../utils/tenant-context';

const allProducts = [
  { id: 1, name: 'iPhone', tenant_id: 'tenant-electronics-001' },
  { id: 2, name: 'Dress', tenant_id: 'tenant-fashion-002' },
  { id: 3, name: 'iPad', tenant_id: 'tenant-electronics-001' }
];

const tenantProducts = filterByTenantId(req, allProducts);
// Returns only products for current tenant
```

### Creating Tenant Queries

```typescript
import { createTenantQuery } from '../utils/tenant-context';

const queryParams = createTenantQuery(req, {
  status: 'active',
  limit: 10
});
// Result: { tenant_id: 'tenant-electronics-001', status: 'active', limit: 10 }
```

## Response Utilities

### Adding Tenant Headers

```typescript
import { addTenantHeaders } from '../utils/tenant-context';

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  // Add tenant context to response headers
  addTenantHeaders(req, res);
  
  // Headers added:
  // X-Tenant-ID: tenant-electronics-001
  // X-Tenant-Name: Electronics Store
  // X-Tenant-Status: active
  // X-Tenant-Features: products,orders,customers
  
  res.json({ data: 'response' });
};
```

### Creating Standardized Responses

```typescript
import { createTenantResponse } from '../utils/tenant-context';

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const products = await getProducts();
  
  const response = createTenantResponse(req, products, 'Products retrieved successfully');
  
  res.json(response);
  // Result:
  // {
  //   success: true,
  //   data: [...products],
  //   tenant_context: {
  //     tenant_id: 'tenant-electronics-001',
  //     tenant_name: 'Electronics Store',
  //     timestamp: '2025-08-07T07:45:00.000Z'
  //   },
  //   message: 'Products retrieved successfully'
  // }
};
```

## Feature Management

### Checking Features

```typescript
import { tenantHasFeature, requireTenantFeature } from '../utils/tenant-context';

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Check if tenant has analytics feature
  if (tenantHasFeature(req, 'analytics')) {
    // Enable analytics functionality
    await trackEvent('product_created', req);
  }
  
  // Require specific feature (throws error if not available)
  try {
    requireTenantFeature(req, 'advanced_reporting');
    // Feature is available, proceed
  } catch (error) {
    return res.status(403).json({ error: error.message });
  }
};
```

## Best Practices

### 1. Always Validate Tenant Context

```typescript
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  const validation = validateTenantContext(req);
  if (!validation.isValid) {
    return res.status(400).json({
      error: 'INVALID_TENANT_CONTEXT',
      details: validation.errors
    });
  }
  
  // Proceed with business logic
};
```

### 2. Use Tenant-Aware Logging

```typescript
import { createTenantLogContext } from '../utils/tenant-context';

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  const logContext = createTenantLogContext(req, {
    operation: 'create_product',
    product_id: 'prod_123'
  });
  
  console.log('Creating product', logContext);
  // Logs with tenant context automatically included
};
```

### 3. Consistent Response Format

```typescript
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const data = await fetchData();
    const response = createTenantResponse(req, data);
    
    addTenantHeaders(req, res);
    res.json(response);
  } catch (error) {
    const logContext = createTenantLogContext(req, { error: error.message });
    console.error('Request failed', logContext);
    
    res.status(500).json({
      error: 'INTERNAL_ERROR',
      tenant_context: {
        tenant_id: getTenantId(req),
        timestamp: new Date().toISOString()
      }
    });
  }
};
```

### 4. Feature-Based Access Control

```typescript
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  // Check multiple features
  const hasAnalytics = tenantHasFeature(req, 'analytics');
  const hasReporting = tenantHasFeature(req, 'reporting');
  
  const data = await fetchBasicData();
  
  if (hasAnalytics) {
    data.analytics = await fetchAnalytics();
  }
  
  if (hasReporting) {
    data.reports = await fetchReports();
  }
  
  res.json(createTenantResponse(req, data));
};
```

## Examples

### Complete Product Endpoint

```typescript
import {
  getTenantId,
  getTenantContext,
  validateTenantContext,
  injectTenantId,
  createTenantResponse,
  addTenantHeaders,
  tenantHasFeature,
  createTenantLogContext
} from '../utils/tenant-context';

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Validate tenant context
    const validation = validateTenantContext(req);
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'INVALID_TENANT_CONTEXT',
        details: validation.errors
      });
    }

    // Get tenant information
    const tenantId = getTenantId(req);
    const context = getTenantContext(req);
    
    // Create log context
    const logContext = createTenantLogContext(req, {
      operation: 'list_products'
    });
    console.log('Fetching products', logContext);

    // Fetch tenant-specific products
    const products = await fetchProductsForTenant(tenantId);
    
    // Add analytics if tenant has the feature
    let responseData = { products };
    if (tenantHasFeature(req, 'analytics')) {
      responseData.analytics = await getProductAnalytics(tenantId);
    }

    // Create standardized response
    const response = createTenantResponse(
      req, 
      responseData, 
      `Found ${products.length} products`
    );

    // Add tenant headers
    addTenantHeaders(req, res);
    
    res.json(response);
    
  } catch (error) {
    const logContext = createTenantLogContext(req, { 
      error: error.message,
      operation: 'list_products'
    });
    console.error('Failed to fetch products', logContext);
    
    res.status(500).json({
      error: 'INTERNAL_ERROR',
      message: 'Failed to fetch products',
      tenant_context: {
        tenant_id: getTenantId(req),
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Validate tenant context
    const validation = validateTenantContext(req);
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'INVALID_TENANT_CONTEXT',
        details: validation.errors
      });
    }

    // Require products feature
    requireTenantFeature(req, 'products');

    // Inject tenant ID into product data
    const productData = injectTenantId(req, req.body);
    
    // Create product
    const product = await createProduct(productData);
    
    // Log creation
    const logContext = createTenantLogContext(req, {
      operation: 'create_product',
      product_id: product.id
    });
    console.log('Product created', logContext);

    // Return response
    const response = createTenantResponse(req, product, 'Product created successfully');
    addTenantHeaders(req, res);
    
    res.status(201).json(response);
    
  } catch (error) {
    const logContext = createTenantLogContext(req, { 
      error: error.message,
      operation: 'create_product'
    });
    console.error('Failed to create product', logContext);
    
    res.status(500).json({
      error: 'INTERNAL_ERROR',
      message: 'Failed to create product',
      tenant_context: {
        tenant_id: getTenantId(req),
        timestamp: new Date().toISOString()
      }
    });
  }
};
```

This guide provides comprehensive documentation for using tenant context in your Medusa v2 multi-tenant application. All utilities are designed to work seamlessly with the existing middleware system and provide consistent, reliable access to tenant information across your application.
