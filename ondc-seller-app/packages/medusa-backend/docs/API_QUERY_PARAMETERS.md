# API Query Parameters Guide

## Overview

All GET endpoints in the tenant-aware API system support comprehensive filtering, sorting, pagination, and search functionality while maintaining strict tenant isolation.

## 🔍 **Supported Query Parameters**

### **📄 Pagination Parameters**

| Parameter | Type | Default | Description | Example |
|-----------|------|---------|-------------|---------|
| `limit` | integer | 20 | Number of items per page | `?limit=10` |
| `offset` | integer | 0 | Number of items to skip | `?offset=20` |

**Example:**
```bash
# Get items 21-30 (page 3 with 10 items per page)
GET /admin/promotions?limit=10&offset=20
```

### **🔄 Sorting Parameters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `order` | string | Sort field and direction | `?order=created_at:desc` |
| `sort` | string | Alias for `order` | `?sort=code:asc` |

**Valid Sort Fields:**
- `created_at` - Creation date
- `updated_at` - Last update date  
- `code` - Promotion/item code
- `status` - Status field
- `type` - Type field

**Sort Directions:**
- `asc` - Ascending (A-Z, 1-9, oldest first)
- `desc` - Descending (Z-A, 9-1, newest first)

**Examples:**
```bash
# Sort by creation date (newest first)
GET /admin/promotions?order=created_at:desc

# Sort by code alphabetically
GET /admin/promotions?sort=code:asc

# Sort by status then by creation date
GET /admin/promotions?order=status:asc,created_at:desc
```

### **🔍 Search Parameters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `q` | string | Search across multiple fields | `?q=WELCOME` |

**Search Behavior:**
- Case-insensitive partial matching
- Searches across relevant text fields (code, name, description)
- Uses `ILIKE` for PostgreSQL compatibility

**Examples:**
```bash
# Search for promotions containing "welcome"
GET /admin/promotions?q=welcome

# Search for products containing "shirt"
GET /admin/products?q=shirt
```

### **🎯 Field-Specific Filters**

#### **Promotions Endpoint (`/admin/promotions`)**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `status` | string | Filter by status | `?status=active` |
| `code` | string | Exact code match | `?code=WELCOME10` |
| `type` | string | Filter by type | `?type=standard` |
| `is_automatic` | boolean | Filter by automatic flag | `?is_automatic=true` |

#### **Products Endpoint (`/admin/products`)**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `status` | string | Filter by status | `?status=published` |
| `title` | string | Filter by title | `?title=T-Shirt` |
| `handle` | string | Filter by handle | `?handle=blue-tshirt` |
| `type_id` | string | Filter by product type | `?type_id=type_123` |
| `collection_id` | string | Filter by collection | `?collection_id=col_123` |

### **📅 Date Range Filters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `created_after` | ISO date | Items created after date | `?created_after=2024-01-01` |
| `created_before` | ISO date | Items created before date | `?created_before=2024-12-31` |
| `updated_after` | ISO date | Items updated after date | `?updated_after=2024-06-01` |
| `updated_before` | ISO date | Items updated before date | `?updated_before=2024-06-30` |

**Examples:**
```bash
# Get promotions created in 2024
GET /admin/promotions?created_after=2024-01-01&created_before=2024-12-31

# Get recently updated products (last 7 days)
GET /admin/products?updated_after=2024-08-01
```

## 🔗 **Combining Parameters**

You can combine multiple parameters for complex queries:

```bash
# Complex promotion query
GET /admin/promotions?status=active&type=standard&order=created_at:desc&limit=5&q=summer

# Complex product query  
GET /admin/products?status=published&created_after=2024-01-01&sort=title:asc&limit=10&offset=20
```

## 📊 **Response Format**

All GET endpoints return responses in this format:

```json
{
  "promotions": [...],           // Array of items
  "count": 5,                    // Items in current response
  "total": 25,                   // Total items matching filters
  "offset": 0,                   // Current offset
  "limit": 20,                   // Current limit
  "has_more": true,              // Whether more items exist
  "_tenant": {
    "id": "my-kirana-store",
    "filtered": true,
    "method": "enhanced_db_connection"
  },
  "_query": {
    "applied_filters": ["status", "q"],
    "sort_by": "created_at:desc",
    "pagination": {
      "current_page": 1,
      "total_pages": 2,
      "per_page": 20
    }
  }
}
```

## 🛡️ **Tenant Isolation**

**All queries automatically include tenant filtering:**
- ✅ Only returns data for the specified tenant (`x-tenant-id` header)
- ✅ Cannot access data from other tenants
- ✅ Maintains security across all filter combinations
- ✅ Counts and pagination respect tenant boundaries

## 🚀 **Usage Examples**

### **Promotions API Examples**

```bash
# Get all active promotions, sorted by newest first
curl 'http://localhost:9000/admin/promotions?status=active&order=created_at:desc' \
  -H 'x-tenant-id: my-kirana-store'

# Search for welcome promotions with pagination
curl 'http://localhost:9000/admin/promotions?q=welcome&limit=5&offset=0' \
  -H 'x-tenant-id: my-kirana-store'

# Get draft promotions created this year
curl 'http://localhost:9000/admin/promotions?status=draft&created_after=2024-01-01' \
  -H 'x-tenant-id: my-kirana-store'
```

### **Products API Examples**

```bash
# Get published products, sorted by title
curl 'http://localhost:9000/admin/products?status=published&sort=title:asc' \
  -H 'x-tenant-id: my-kirana-store'

# Search for shirt products with pagination
curl 'http://localhost:9000/admin/products?q=shirt&limit=10&offset=0' \
  -H 'x-tenant-id: my-kirana-store'

# Get products from specific collection
curl 'http://localhost:9000/admin/products?collection_id=col_123&limit=20' \
  -H 'x-tenant-id: my-kirana-store'
```

## ⚡ **Performance Tips**

1. **Use pagination** for large datasets (`limit` + `offset`)
2. **Combine filters** to reduce result sets before sorting
3. **Use specific filters** instead of broad searches when possible
4. **Index commonly filtered fields** in the database
5. **Cache frequently accessed queries** on the client side

## 🔧 **Implementation Status**

| Endpoint | Pagination | Sorting | Search | Field Filters | Date Filters |
|----------|------------|---------|--------|---------------|--------------|
| `/admin/promotions` | ✅ | ✅ | ✅ | ✅ | ✅ |
| `/admin/products` | ✅ | ✅ | ✅ | ✅ | ✅ |
| `/admin/orders` | 🔄 | 🔄 | 🔄 | 🔄 | 🔄 |
| `/admin/customers` | 🔄 | 🔄 | 🔄 | 🔄 | 🔄 |

**Legend:**
- ✅ Implemented and tested
- 🔄 Implementation in progress
- ❌ Not yet implemented

## 🎯 **Next Steps**

To implement similar functionality for other endpoints:

1. **Copy the helper functions** from `promotions/route.ts`
2. **Adapt field names** for the specific entity
3. **Update valid sort fields** array
4. **Add entity-specific filters**
5. **Test with various query combinations**

All endpoints will maintain the same query parameter interface for consistency!
