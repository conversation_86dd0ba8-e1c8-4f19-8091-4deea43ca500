# Multi-Tenant ONDC Seller App API Documentation

## 🚀 **Quick Start**

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Medusa v2 backend running on port 9000

### Authentication Setup

#### 1. Admin Authentication
```bash
# Get admin JWT token
curl -X POST http://localhost:9000/auth/user/emailpass \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "supersecret"}'
```

#### 2. Store API Key
Use the publishable API key: `pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0`

### Tenant Configuration
Set the `x-tenant-id` header to one of:
- `tenant-electronics-001` - Electronics Store
- `tenant-fashion-002` - Fashion Store  
- `default` - Default Store

## 📚 **API Documentation**

### OpenAPI Specification
- **File**: `openapi.yaml`
- **Format**: OpenAPI 3.0.3
- **Usage**: Import into Swagger UI, Redoc, or any OpenAPI-compatible tool

### Postman Collection
- **Collection**: `Multi-Tenant-ONDC-Seller-App.postman_collection.json`
- **Environment**: `Multi-Tenant-ONDC-Development.postman_environment.json`
- **Usage**: Import both files into Postman for immediate testing

## 🏗️ **API Endpoints Overview**

### Admin Endpoints (Require JWT Token)

#### Get Tenant Configuration
```http
GET /admin/tenant
Authorization: Bearer {jwt_token}
x-tenant-id: tenant-electronics-001
```

#### Test Multi-Tenant Isolation
```http
GET /admin/test-multi-tenant
Authorization: Bearer {jwt_token}
x-tenant-id: tenant-electronics-001
```

### Store Endpoints (Require API Key)

#### Get Store Information
```http
GET /store/test-info
x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0
x-tenant-id: tenant-electronics-001
```

#### Get Store Products
```http
GET /store/test-products
x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0
x-tenant-id: tenant-electronics-001
```

#### Get Product Details
```http
GET /store/test-products/{product_id}
x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0
x-tenant-id: tenant-electronics-001
```

## 🧪 **Testing Examples**

### 1. Test Electronics Store
```bash
# Get store info
curl -H "x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0" \
     -H "x-tenant-id: tenant-electronics-001" \
     http://localhost:9000/store/test-info

# Get products
curl -H "x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0" \
     -H "x-tenant-id: tenant-electronics-001" \
     http://localhost:9000/store/test-products

# Get specific product
curl -H "x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0" \
     -H "x-tenant-id: tenant-electronics-001" \
     http://localhost:9000/store/test-products/prod_electronics_001
```

### 2. Test Fashion Store
```bash
# Get store info
curl -H "x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0" \
     -H "x-tenant-id: tenant-fashion-002" \
     http://localhost:9000/store/test-info

# Get products
curl -H "x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0" \
     -H "x-tenant-id: tenant-fashion-002" \
     http://localhost:9000/store/test-products
```

### 3. Test Cross-Tenant Security
```bash
# This should fail with 404
curl -H "x-publishable-api-key: pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0" \
     -H "x-tenant-id: tenant-electronics-001" \
     http://localhost:9000/store/test-products/prod_fashion_001
```

## 🔐 **Security Features**

### Multi-Tenant Isolation
- Database-level tenant isolation via `tenant_id` field
- API-level filtering based on `x-tenant-id` header
- Cross-tenant access prevention

### Authentication
- **Admin APIs**: JWT token authentication
- **Store APIs**: Publishable API key authentication
- **Headers**: Required authentication headers for all endpoints

### Data Protection
- Tenant-specific product catalogs
- Isolated customer data
- Secure configuration management

## 📊 **Response Formats**

### Success Response
```json
{
  "store": {
    "id": "tenant-electronics-001",
    "name": "Electronics Store",
    "domain": "electronics.ondc-seller.com"
  },
  "products": [...],
  "tenant_id": "tenant-electronics-001",
  "multi_tenant": true
}
```

### Error Response
```json
{
  "error": "Product not found in this store",
  "product_id": "prod_fashion_001",
  "tenant_id": "tenant-electronics-001",
  "note": "Product exists but doesn't belong to this tenant"
}
```

## 🛠️ **Development Tools**

### Postman Setup
1. Import `Multi-Tenant-ONDC-Seller-App.postman_collection.json`
2. Import `Multi-Tenant-ONDC-Development.postman_environment.json`
3. Select the "Multi-Tenant ONDC Development" environment
4. Start testing with pre-configured requests

### OpenAPI Tools
- **Swagger UI**: Paste `openapi.yaml` content
- **Redoc**: Generate documentation from OpenAPI spec
- **Code Generation**: Use OpenAPI generators for client SDKs

## 📋 **Testing Checklist**

- [ ] Admin authentication working
- [ ] Store API key authentication working
- [ ] Electronics tenant returns correct store info
- [ ] Fashion tenant returns correct store info
- [ ] Product filtering works per tenant
- [ ] Cross-tenant access is blocked
- [ ] All endpoints return proper HTTP status codes
- [ ] Error responses include helpful information

## 🚨 **Troubleshooting**

### Common Issues

#### 1. Authentication Failed
- Verify JWT token is valid and not expired
- Check API key is correctly set in header
- Ensure proper Authorization header format

#### 2. Tenant Not Found
- Verify `x-tenant-id` header is set
- Check tenant ID matches expected values
- Ensure tenant exists in database

#### 3. Product Not Found
- Verify product ID exists in database
- Check product belongs to the specified tenant
- Ensure product status is "published"

### Debug Commands
```bash
# Check server health
curl http://localhost:9000/health

# Verify database connection
docker exec -it strapi-postgres psql -U strapi -d medusa_backend -c "SELECT COUNT(*) FROM product;"

# Check tenant data
docker exec -it strapi-postgres psql -U strapi -d medusa_backend -c "SELECT tenant_id, COUNT(*) FROM product GROUP BY tenant_id;"
```

## 📞 **Support**

For issues or questions:
- Check the testing report: `MULTI_TENANT_TESTING_REPORT.md`
- Review API responses for error details
- Verify authentication and tenant headers
- Check database connectivity and data integrity

---

**Last Updated**: 2025-01-02  
**API Version**: 2.0.0  
**Medusa Version**: v2  
**Environment**: Development
