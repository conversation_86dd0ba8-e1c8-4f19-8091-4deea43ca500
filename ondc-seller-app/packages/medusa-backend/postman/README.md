# Medusa v2 Multi-Tenant API - Postman Collection

## 📋 **Overview**

This comprehensive Postman collection provides complete API testing capabilities for the Medusa v2 Multi-Tenant ONDC Seller Backend. It includes authentication, tenant management, product operations, customer management, order processing, and store API endpoints.

## 🚀 **Quick Start**

### **1. Import Collection and Environment**

#### **Import Main Collection:**
```
File: Medusa-v2-Multi-Tenant-Complete.postman_collection.json
```

#### **Import Environment (Choose One):**
- **Electronics Store**: `Electronics-Store-Environment.postman_environment.json`
- **Fashion Store**: `Fashion-Store-Environment.postman_environment.json`
- **Development**: `Development-Environment.postman_environment.json`

### **2. Setup Authentication**
1. Select your imported environment
2. Run the **"Admin Login"** request in the 🔐 Authentication folder
3. The auth token will be automatically saved to your environment

### **3. Test Multi-Tenancy**
1. Switch between environments to test different tenants
2. Use the 🏢 Tenant Management folder to verify tenant configurations
3. Each tenant has isolated data (products, customers, orders)

## 📁 **Collection Structure**

### **🔐 Authentication**
- **Admin Login** - Get JWT token (auto-saves to environment)
- **Get Current User** - Verify authentication
- **Health Check** - Server status

### **🏢 Tenant Management**
- **Get Current Tenant** - Tenant configuration with ONDC settings
- **Test Electronics Tenant** - Electronics store validation
- **Test Fashion Tenant** - Fashion store validation

### **🛍️ Product Management**
- **List All Products** - View all products (tenant-filtered)
- **Get Product by ID** - Single product details
- **Search Products** - Product search with tenant context

### **👥 Customer Management**
- **List All Customers** - View all customers (tenant-filtered)
- **Get Customer by ID** - Single customer details

### **📦 Order Management**
- **List All Orders** - View all orders (tenant-filtered)

### **🛒 Store API**
- **Get Store Information** - Public store details
- **Browse Products** - Customer-facing product catalog

## 🌍 **Environment Variables**

### **Required Variables:**
| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | Medusa server URL | `http://localhost:9000` |
| `tenant_id` | Current tenant identifier | `tenant-electronics-001` |
| `admin_email` | Admin login email | `<EMAIL>` |
| `admin_password` | Admin login password | `supersecret` |
| `auth_token` | JWT token (auto-populated) | `eyJhbGciOiJIUzI1NiIs...` |

### **Tenant-Specific Variables:**
| Variable | Electronics | Fashion |
|----------|-------------|---------|
| `tenant_id` | `tenant-electronics-001` | `tenant-fashion-002` |
| `tenant_name` | `Electronics Store` | `Fashion Store` |
| `tenant_domain` | `electronics.ondc-seller.com` | `fashion.ondc-seller.com` |
| `customer_id` | `cust_electronics_001` | `cust_fashion_001` |
| `product_id` | `prod_electronics_001` | `prod_fashion_001` |

## 🧪 **Testing Scenarios**

### **Scenario 1: Basic Authentication**
1. Run **Health Check** to verify server
2. Run **Admin Login** to authenticate
3. Run **Get Current User** to verify token

### **Scenario 2: Tenant Switching**
1. Import Electronics environment
2. Run **Get Current Tenant** - should return Electronics config
3. Switch to Fashion environment
4. Run **Get Current Tenant** - should return Fashion config

### **Scenario 3: Data Isolation**
1. Use Electronics environment
2. Run **List All Products** - should show electronics products
3. Switch to Fashion environment
4. Run **List All Products** - should show fashion products

### **Scenario 4: Store API Testing**
1. Run **Get Store Information** (no auth required)
2. Run **Browse Products** (public product catalog)
3. Verify tenant-specific products are returned

## 🔧 **Advanced Usage**

### **Custom Headers**
All admin endpoints automatically include:
- `Authorization: Bearer {{auth_token}}`
- `x-tenant-id: {{tenant_id}}`

### **Dynamic Variables**
The collection uses Postman dynamic variables:
- `{{$randomInt}}` - Random integers
- `{{$timestamp}}` - Current timestamp
- `{{$isoTimestamp}}` - ISO timestamp

### **Test Scripts**
Each request includes test scripts that:
- Validate response status codes
- Check response structure
- Verify tenant isolation
- Auto-save important values to environment

## 📊 **Sample Data**

### **Electronics Store Data:**
- **Products**: iPhone 15 Pro, Samsung Galaxy S24 Ultra, MacBook Pro 14", Dell XPS 13, Sony WH-1000XM5
- **Customers**: John Doe, Sarah Johnson, Mike Wilson
- **Domain**: electronics.ondc-seller.com

### **Fashion Store Data:**
- **Products**: Men's Casual Shirt, Women's Summer Dress, Men's Formal Blazer, Women's Denim Jeans, Designer Handbag
- **Customers**: Emma Davis, Alex Brown, Lisa Miller
- **Domain**: fashion.ondc-seller.com

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **Authentication Failed**
- Verify `admin_email` and `admin_password` in environment
- Check if Medusa server is running on correct port
- Run Health Check first

#### **Tenant Not Found**
- Verify `tenant_id` matches available tenants
- Check x-tenant-id header is included
- Ensure tenant data was seeded correctly

#### **Empty Product/Customer Lists**
- Verify you're using correct tenant environment
- Check if sample data was seeded
- Confirm tenant isolation is working

#### **Server Connection Issues**
- Verify `base_url` in environment
- Check if Medusa server is running (`npm run dev`)
- Ensure port 9000 is accessible

## 📝 **API Documentation**

### **Authentication Endpoints**
```
POST /auth/user/emailpass - Admin login
GET  /auth/user/me        - Current user info
GET  /health              - Server health check
```

### **Admin Endpoints (Require Auth + Tenant Header)**
```
GET  /admin/tenant        - Tenant configuration
GET  /admin/products      - List products
GET  /admin/customers     - List customers
GET  /admin/orders        - List orders
```

### **Store Endpoints (Public + Tenant Header)**
```
GET  /store               - Store information
GET  /store/products      - Browse products
```

## 🎯 **Next Steps**

1. **Extend Collections**: Add more CRUD operations (POST, PUT, DELETE)
2. **Add Workflows**: Create multi-step testing workflows
3. **Environment Management**: Create staging/production environments
4. **Automated Testing**: Set up Newman for CI/CD integration
5. **Documentation**: Generate API documentation from collection

## 📞 **Support**

For issues or questions:
1. Check the troubleshooting section above
2. Verify environment variables are correctly set
3. Ensure Medusa server is running with tenant data seeded
4. Review the TENANT_DATA_SEEDING_SUMMARY.md for data verification

---

**Collection Version**: 1.0.0  
**Last Updated**: 2025-01-02  
**Compatible with**: Medusa v2 Multi-Tenant Backend
