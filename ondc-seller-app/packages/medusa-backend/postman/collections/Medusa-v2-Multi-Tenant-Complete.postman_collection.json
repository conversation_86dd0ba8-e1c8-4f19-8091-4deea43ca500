{"info": {"name": "Medusa v2 Multi-Tenant Complete API", "description": "Complete API collection for Medusa v2 Multi-Tenant ONDC Seller Backend\n\n## Features\n- Multi-tenant support with tenant isolation\n- ONDC-compliant configurations\n- Complete CRUD operations for products, customers, orders\n- Store API for customer-facing operations\n- Authentication and authorization\n\n## Setup Instructions\n1. Import one of the environment files:\n   - Electronics-Store-Environment.json\n   - Fashion-Store-Environment.json\n   - Development-Environment.json\n\n2. Run the \"Admin Login\" request first to get authentication token\n\n3. Use the tenant-specific endpoints with proper x-tenant-id headers\n\n## Environment Variables Required\n- base_url: http://localhost:9000\n- tenant_id: tenant-electronics-001 or tenant-fashion-002\n- admin_email: <EMAIL>\n- admin_password: supersecret\n- auth_token: (auto-populated after login)\n\n## Multi-Tenancy\nThis collection supports multi-tenant operations. Switch between tenants by:\n1. Changing the environment\n2. Using different x-tenant-id headers\n3. Each tenant has isolated data (products, customers, orders)\n\n## ONDC Integration\nTenant configurations include ONDC-specific settings:\n- Participant IDs\n- Subscriber IDs\n- BPP IDs\n- Domain configurations\n- Regional settings", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "medusa-v2-multi-tenant-complete", "version": {"major": 1, "minor": 0, "patch": 0}}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "console.log('Current Tenant:', pm.environment.get('tenant_id'));", "console.log('Base URL:', pm.environment.get('base_url'));", "", "// Check if auth token exists for admin endpoints", "const isAdminEndpoint = pm.request.url.path.includes('admin');", "if (isAdminEndpoint && !pm.environment.get('auth_token')) {", "    console.log('Warning: No auth token found for admin endpoint. Please run authentication first.');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test(\"Response has valid JSON\", function () {", "    pm.response.to.be.json;", "});"]}}], "variable": [{"key": "collection_version", "value": "1.0.0", "type": "string"}, {"key": "api_version", "value": "v2", "type": "string"}], "item": [{"name": "🔐 Authentication", "description": "Authentication and authorization endpoints", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    ", "    // Save token to environment variable", "    pm.environment.set('auth_token', jsonData.token);", "    ", "    console.log('✅ Token saved to environment');", "});", "", "pm.test(\"Response has user data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData.user).to.have.property('email');", "});"]}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{admin_email}}\",\n  \"password\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/user/emailpass", "host": ["{{base_url}}"], "path": ["auth", "user", "emailpass"]}, "description": "🔑 Authenticate admin user and get JW<PERSON> token\n\n**Required Environment Variables:**\n- admin_email\n- admin_password\n\n**Response:**\n- Sets auth_token environment variable\n- Returns user information"}}, {"name": "Get Current User", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/user/me", "host": ["{{base_url}}"], "path": ["auth", "user", "me"]}, "description": "👤 Get current authenticated user information"}}, {"name": "Health Check", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "🏥 Check if the Medusa server is running and healthy"}}]}, {"name": "🏢 Tenant Management", "description": "Multi-tenant configuration and management", "item": [{"name": "Get Current Tenant", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has tenant data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('tenant');", "    pm.expect(jsonData.tenant).to.have.property('id');", "    pm.expect(jsonData.tenant.id).to.eql(pm.environment.get('tenant_id'));", "});", "", "pm.test(\"Tenant has ONDC configuration\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.tenant).to.have.property('settings');", "    pm.expect(jsonData.tenant.settings).to.have.property('ondcConfig');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "🏢 Get current tenant configuration including ONDC settings\n\n**Headers:**\n- x-tenant-id: Current tenant identifier\n\n**Response:**\n- Tenant configuration\n- ONDC settings\n- Multi-tenancy status"}}, {"name": "Test Electronics Tenant", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "tenant-electronics-001", "type": "text"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "📱 Test Electronics Store tenant configuration"}}, {"name": "Test Fashion Tenant", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "tenant-fashion-002", "type": "text"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "👗 Test Fashion Store tenant configuration"}}]}, {"name": "🛍️ Product Management", "description": "Product CRUD operations with multi-tenant support", "item": [{"name": "List All Products", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/products", "host": ["{{base_url}}"], "path": ["admin", "products"]}, "description": "📦 Get all products (shows tenant isolation in database)"}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["admin", "products", "{{product_id}}"]}, "description": "🔍 Get specific product by ID with tenant validation"}}, {"name": "Search Products", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/products?q=iPhone", "host": ["{{base_url}}"], "path": ["admin", "products"], "query": [{"key": "q", "value": "iPhone"}]}, "description": "🔎 Search products by title with tenant filtering"}}]}, {"name": "👥 Customer Management", "description": "Customer CRUD operations with multi-tenant support", "item": [{"name": "List All Customers", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/customers", "host": ["{{base_url}}"], "path": ["admin", "customers"]}, "description": "👥 Get all customers (shows tenant isolation in database)"}}, {"name": "Get Customer by ID", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/customers/{{customer_id}}", "host": ["{{base_url}}"], "path": ["admin", "customers", "{{customer_id}}"]}, "description": "👤 Get specific customer by ID with tenant validation"}}]}, {"name": "📦 Order Management", "description": "Order processing and management with multi-tenant support", "item": [{"name": "List All Orders", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/orders", "host": ["{{base_url}}"], "path": ["admin", "orders"]}, "description": "📋 Get all orders (shows tenant isolation in database)"}}]}, {"name": "🛒 Store API", "description": "Customer-facing store endpoints", "item": [{"name": "Get Store Information", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/store", "host": ["{{base_url}}"], "path": ["store"]}, "description": "🏪 Get store information with tenant context"}}, {"name": "Browse Products", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/store/products", "host": ["{{base_url}}"], "path": ["store", "products"]}, "description": "🛍️ Browse published products in store with tenant filtering"}}]}]}