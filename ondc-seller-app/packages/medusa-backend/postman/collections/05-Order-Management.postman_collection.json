{"info": {"name": "05 - Order Management", "description": "Order processing and management with multi-tenant support", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Check if auth token exists", "if (!pm.environment.get('auth_token')) {", "    console.log('Warning: No auth token found. Please run authentication first.');", "}"]}}], "variable": [], "item": [{"name": "List All Orders", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has orders array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('orders');", "    pm.expect(jsonData.orders).to.be.an('array');", "});", "", "pm.test(\"Orders have tenant_id\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.orders.length > 0) {", "        pm.expect(jsonData.orders[0]).to.have.property('tenant_id');", "    }", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/orders", "host": ["{{base_url}}"], "path": ["admin", "orders"]}, "description": "Get all orders (shows tenant isolation in database)"}}, {"name": "Get Order by ID", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has order data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('order');", "    pm.expect(jsonData.order).to.have.property('id');", "});", "", "pm.test(\"Order has correct tenant\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.order.tenant_id) {", "        pm.expect(jsonData.order.tenant_id).to.eql(pm.environment.get('tenant_id'));", "    }", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/orders/{{order_id}}", "host": ["{{base_url}}"], "path": ["admin", "orders", "{{order_id}}"]}, "description": "Get specific order by ID with tenant validation"}}, {"name": "Get Orders with Status Filter", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Filtered orders returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('orders');", "    pm.expect(jsonData.orders).to.be.an('array');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/orders?status=pending", "host": ["{{base_url}}"], "path": ["admin", "orders"], "query": [{"key": "status", "value": "pending"}]}, "description": "Get orders filtered by status with tenant context"}}, {"name": "Get Orders with Date Range", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Date filtered orders returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('orders');", "    pm.expect(jsonData.orders).to.be.an('array');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/orders?created_at[gte]=2025-01-01&created_at[lte]=2025-12-31", "host": ["{{base_url}}"], "path": ["admin", "orders"], "query": [{"key": "created_at[gte]", "value": "2025-01-01"}, {"key": "created_at[lte]", "value": "2025-12-31"}]}, "description": "Get orders within date range with tenant filtering"}}, {"name": "Get Orders with Pagination", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Pagination data present\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('count');", "    pm.expect(jsonData).to.have.property('offset');", "    pm.expect(jsonData).to.have.property('limit');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/orders?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["admin", "orders"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}, "description": "Get orders with pagination parameters"}}, {"name": "Update Order Status", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Order updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('order');", "    pm.expect(jsonData.order).to.have.property('status');", "});"]}}], "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"fulfilled\",\n  \"metadata\": {\n    \"updated_via\": \"postman\",\n    \"updated_at\": \"{{$isoTimestamp}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/orders/{{order_id}}", "host": ["{{base_url}}"], "path": ["admin", "orders", "{{order_id}}"]}, "description": "Update order status with tenant validation"}}, {"name": "Get Order Analytics", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Analytics data returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('orders');", "    pm.expect(jsonData).to.have.property('count');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/orders?fields=id,status,total,created_at&expand=customer", "host": ["{{base_url}}"], "path": ["admin", "orders"], "query": [{"key": "fields", "value": "id,status,total,created_at"}, {"key": "expand", "value": "customer"}]}, "description": "Get order analytics data with tenant filtering"}}]}