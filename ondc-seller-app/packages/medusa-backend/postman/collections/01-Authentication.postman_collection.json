{"info": {"name": "01 - Authentication", "description": "Authentication endpoints for Medusa v2 Multi-Tenant Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "<PERSON><PERSON><PERSON>"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request script for authentication collection"]}}], "variable": [], "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["// Test script to extract and save token", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    ", "    // Save token to environment variable", "    pm.environment.set('auth_token', jsonData.token);", "    ", "    console.log('Token saved to environment:', jsonData.token);", "});", "", "pm.test(\"Response has user data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData.user).to.have.property('email');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{admin_email}}\",\n  \"password\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/user/emailpass", "host": ["{{base_url}}"], "path": ["auth", "user", "emailpass"]}, "description": "Authenticate admin user and get JWT token"}}, {"name": "Get Current User", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has user data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData.user).to.have.property('email');", "    pm.expect(jsonData.user.email).to.eql(pm.environment.get('admin_email'));", "});"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/auth/user/me", "host": ["{{base_url}}"], "path": ["auth", "user", "me"]}, "description": "Get current authenticated user information"}}, {"name": "Logout", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Clear token from environment", "pm.environment.set('auth_token', '');"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/auth/user/logout", "host": ["{{base_url}}"], "path": ["auth", "user", "logout"]}, "description": "Logout current user and invalidate token"}}, {"name": "Health Check", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Server is healthy\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData.status).to.eql('ok');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the Medusa server is running and healthy"}}]}