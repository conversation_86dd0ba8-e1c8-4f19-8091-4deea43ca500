#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Set the working directory to the medusa-backend package
process.chdir(__dirname);

// Set environment variables to ensure proper module resolution
process.env.NODE_PATH = path.join(__dirname, 'node_modules');
process.env.TS_NODE_PROJECT = path.join(__dirname, 'tsconfig.json');

// Run medusa develop with proper context
const medusa = spawn('npx', ['medusa', 'develop'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_PATH: path.join(__dirname, 'node_modules'),
    TS_NODE_PROJECT: path.join(__dirname, 'tsconfig.json'),
  },
  cwd: __dirname
});

medusa.on('close', (code) => {
  process.exit(code);
});

medusa.on('error', (err) => {
  console.error('Failed to start medusa:', err);
  process.exit(1);
});
